from telethon import TelegramClient, events
import asyncio
import re
import logging
import os
from datetime import datetime
import pytz
from openpyxl import Workbook, load_workbook
from openpyxl.styles import Pattern<PERSON>ill
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# FDV1 Scanner specific credentials
API_ID = os.getenv("FDV1_API_ID")
API_HASH = os.getenv("FDV1_API_HASH")
PHONE = os.getenv("FDV1_PHONE")
DESTINATION_BOT = os.getenv("SOUL_SCANNER_BOT", "@soul_scanner_bot")

# FDV1-specific configuration
SOURCE_USERNAME = "@gmgnsignals"
SESSION_FILE = "FDV1/FDV1_Scanner.session"  # Session inside folder
EXCEL_FILE = "FDV1_Scanner_data.xlsx"       # Excel in main directory
WHITELIST = ["FDV in 5 min"]
BLACKLIST = ["SellAmount"]

# Script settings
RATE_LIMIT_DELAY = 3
WAIT_TIME = 5

# 🚀 ULTRA-FAST Global variables
client = None
processed_tokens = set()
processing_lock = asyncio.Lock()  # Keep for Excel safety
last_processed_message_id = None
message_queue = asyncio.Queue(maxsize=500)  # GENIUS: High-speed queue
WORKER_COUNT = 3  # ULTRA-FAST: Multiple parallel workers
stats = {"processed": 0, "queued": 0, "responses": 0}

# 🔄 AUTOMATED RESCANNING SYSTEM
token_timestamps = {}  # Store token scan times: {token_address: initial_scan_time}
rescan_intervals = {
    "1h": 3600,    # 1 hour in seconds
    "5h": 18000,   # 5 hours in seconds
    "24h": 86400   # 24 hours in seconds
}
rescanning_active = True
import time  # For performance timing

HEADERS = [
    # Your specified order first
    "Timestamp", "Token Address", "Token Name", "Warnings", "Age", "MC", "T-MC",
    "1h T-MC", "5h T-MC", "24h T-MC",  # 🔄 AUTOMATED RESCANNING COLUMNS
    "Liq", "Liq SOL", "First 20 Percentage", "First 20", "Whale Fish Pattern",
    "Made", "Volume", "Price", "Scans", "Hodls", "Top Holders", "Snipers",
    "Snipers Percentage",
    # Remaining columns in any order
    "Dex Paid", "High", "Dev", "LP Burnt", "Bundled", "Airdrop", "Burnt",
    "Sold", "Bond"
]

# Morocco timezone
MOROCCO_TZ = pytz.timezone('Africa/Casablanca')

# Logging configuration (console only)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def initialize_excel():
    """Initialize Excel file with headers if it doesn't exist"""
    if not os.path.exists(EXCEL_FILE):
        wb = Workbook()
        ws = wb.active
        ws.append(HEADERS)
        wb.save(EXCEL_FILE)
        logger.info(f"✅ Created new Excel file: {EXCEL_FILE}")
    else:
        logger.info(f"📊 Using existing Excel file: {EXCEL_FILE}")

def extract_token_from_text(message_text):
    """Extract token from plain text (last 4 characters must be 'pump')."""
    token_pattern = r"([A-Za-z0-9]{39,40}pump)"
    token_matches = re.findall(token_pattern, message_text)
    if token_matches:
        return token_matches[0]
    return None

# Source Age extraction removed as requested

def extract_data_from_scanner_response(message_text, token_address, source_age):
    """Extract data from scanner bot response"""
    data = {
        "Token Address": token_address,
        "Token Name": "N/A",
        "Warnings": "N/A",
        "Age": "N/A",
        "Source Age": source_age,
        "MC": "N/A",
        "T-MC": "N/A",
        "Liq": "N/A",
        "Liq SOL": "N/A",
        "Volume": "N/A",
        "Price": "N/A",
        "Dex Paid": "N/A",
        "Scans": "N/A",
        "Hodls": "N/A",
        "High": "N/A",
        "Snipers": "N/A",
        "Snipers Percentage": "N/A",
        "Dev": "N/A",
        "Top Holders": "N/A",
        "LP Burnt": "N/A",
        "Bundled": "N/A",
        "Airdrop": "N/A",
        "Burnt": "N/A",
        "Sold": "N/A",
        "Made": "N/A",
        "Bond": "N/A",
        "First 20 Percentage": "N/A",
        "First 20": "N/A",
        "Whale Fish Pattern": "N/A"
    }

    # Token Name
    token_name_match = re.search(r"\*\*\$([A-Za-z0-9]+)\*\*", message_text)
    if token_name_match:
        data["Token Name"] = token_name_match.group(1)

    # Warnings extraction
    warnings = re.findall(r"(🚨|⚠️)?\s*__([^__]+)__", message_text)
    if warnings:
        filtered_warnings = [
            f"{icon} {warning}" if icon else warning
            for icon, warning in warnings
            if any(keyword.lower() in warning.lower()
                 for keyword in ["High", "Migrating", "Low", "Dev", "Sniped", "Abnormal"])
        ]
        if filtered_warnings:
            data["Warnings"] = " | ".join(filtered_warnings)

    # Age
    age_match = re.search(r"🕒 \*\*Age:\*\* ([^\n]+)", message_text)
    if age_match:
        data["Age"] = age_match.group(1).strip()

    # MC (Market Cap)
    mc_match = re.search(r"💰 \*\*MC:\*\* ([^\s•]+)", message_text)
    if mc_match:
        data["MC"] = mc_match.group(1).strip()

    # T-MC (Target Market Cap)
    t_mc_match = re.search(r"🔝 __([^_]+)__", message_text)
    if t_mc_match:
        data["T-MC"] = t_mc_match.group(1).strip()

    # Liquidity
    liq_match = re.search(r"💧 \*\*Liq:\*\* ([^\s•]+)", message_text)
    if liq_match:
        data["Liq"] = liq_match.group(1).strip()

    # Liquidity SOL
    liq_sol_match = re.search(r"💧.*?(\d+\.?\d* SOL)", message_text)
    if liq_sol_match:
        data["Liq SOL"] = liq_sol_match.group(1)

    # Volume - Updated pattern for: 📈 **Vol:** __1h__: $527.9K | __1d__: $1.4M
    volume_match = re.search(r"📈 \*\*Vol:\*\* ([^📈\n]+)", message_text)
    if volume_match:
        data["Volume"] = volume_match.group(1).strip()

    # Price
    price_match = re.search(r"📈 \*\*Price:\*\* ([^*\n]+)", message_text)
    if price_match:
        data["Price"] = price_match.group(1).strip()

    # Dex Paid
    if "✅" in message_text and "Dex" in message_text:
        data["Dex Paid"] = "✅"
    elif "❌" in message_text and "Dex" in message_text:
        data["Dex Paid"] = "❌"

    # Scans - Updated pattern for: ⚡ **Scans:** 627 | 🔗 [X](https://x.com/...)
    scans_match = re.search(r"⚡ \*\*Scans:\*\* (\d+)", message_text)
    if scans_match:
        data["Scans"] = scans_match.group(1)

    # Hodls - Updated pattern for: **👥 **[**Hodls**](...): 1,438 • Top: 15.5%
    hodls_match = re.search(r"\*\*👥 \*\*\[\*\*Hodls\*\*\]\([^)]+\): ([\d,]+)", message_text)
    if hodls_match:
        data["Hodls"] = hodls_match.group(1)

    # High - Multiple patterns for: ┗ High: [5.2%](link) or ┗ High: 5.2% or High: 5.2%
    high_match = re.search(r"┗ High: \[(\d+\.?\d*%)\]", message_text)  # [5.2%](link) format
    if not high_match:
        high_match = re.search(r"┗ High: (\d+\.?\d*%)", message_text)  # 5.2% format
    if not high_match:
        # Alternative pattern for other High formats
        high_match = re.search(r"High: (\d+\.?\d*%)", message_text)

    if high_match:
        data["High"] = high_match.group(1)

    # Snipers
    snipers_match = re.search(r"🎯 \*\*Snipers:\*\* (\d+)", message_text)
    if snipers_match:
        data["Snipers"] = snipers_match.group(1)

    # Snipers Percentage
    snipers_percentage_match = re.search(r"🎯.*?(\d+\.?\d*%)", message_text)
    if snipers_percentage_match:
        data["Snipers Percentage"] = snipers_percentage_match.group(1)

    # Dev
    dev_match = re.search(r"🛠️ Dev.*?(\d+ SOL)", message_text)
    if dev_match:
        data["Dev"] = dev_match.group(1)

    # Top Holders - Updated pattern for: **👥 **[**Hodls**](...): 1,438 • Top: 15.5%
    top_holders_match = re.search(r"\*\*👥 \*\*\[\*\*Hodls\*\*\]\([^)]+\): [\d,]+ • Top: (\d+\.?\d*%)", message_text)
    if top_holders_match:
        data["Top Holders"] = top_holders_match.group(1)

    # LP Burnt
    lp_match = re.search(r"\*\*:\*\* (\d+%) Burnt", message_text)
    if lp_match:
        data["LP Burnt"] = lp_match.group(1)

    # Sold
    sold_match = re.search(r"\| Sold:\s*(\d+\.?\d*%)", message_text)
    if sold_match:
        data["Sold"] = sold_match.group(1)

    # Made patterns
    made_match = re.search(r"Made: (\d+)", message_text)
    if made_match:
        data["Made"] = made_match.group(1)

    # Bond patterns
    bond_match = re.search(r"Bond: (\d+)", message_text)
    if bond_match:
        data["Bond"] = bond_match.group(1)

    # Bundled patterns
    bundled_match = re.search(r"Bundled: (\d+%)", message_text)
    if bundled_match:
        data["Bundled"] = bundled_match.group(1)

    # Airdrop patterns
    airdrop_match = re.search(r"Airdrop: (\d+%)", message_text)
    if airdrop_match:
        data["Airdrop"] = airdrop_match.group(1)

    # Burnt patterns (different from LP Burnt)
    burnt_match = re.search(r"Burnt: ([^🔥\n]+)", message_text)
    if burnt_match:
        data["Burnt"] = burnt_match.group(1).strip()

    # First 20 detailed patterns - extract from the summary line
    # Pattern: [🎯 First 20](link): 26% | 11 🐟 • 19%
    first20_detailed = re.search(r"\*\*:\*\* (\d+%) \| (\d+) ([🛠🐟🍤🐳🌱]) • (\d+%)", message_text)
    if first20_detailed:
        data["First 20 Percentage"] = first20_detailed.group(1)
        count = first20_detailed.group(2)
        emoji = first20_detailed.group(3)
        percentage = first20_detailed.group(4)
        data["First 20"] = f"{count} {emoji} • {percentage}"

    # Extract whale/fish patterns and analyze them
    whale_fish_patterns = extract_whale_fish_patterns(message_text)
    if whale_fish_patterns:
        data.update(whale_fish_patterns)

    return data

def extract_whale_fish_patterns(text):
    """Extract whale/fish emoji patterns"""
    patterns = {}
    
    # Extract the detailed holder pattern from the lines with solscan links
    # Look for pattern like: [🛠](https://solscan.io/account/...)[🐟](https://solscan.io/account/...)
    holder_pattern_match = re.findall(r'\[([🛠🐟🍤🐳🌱])\]\(https://solscan\.io/account/[^)]+\)', text)
    
    if holder_pattern_match:
        # Combine all holder emojis to create the full pattern
        all_patterns = ''.join(holder_pattern_match)
        patterns['Whale Fish Pattern'] = all_patterns
    
    return patterns

def parse_market_cap_value(mc_text):
    """Parse market cap text and return numeric value in thousands"""
    if not mc_text or mc_text == "N/A":
        return 0
    mc_clean = mc_text.replace("$", "").replace(",", "").upper()
    try:
        if "K" in mc_clean:
            value = float(mc_clean.replace("K", ""))
            return value
        elif "M" in mc_clean:
            value = float(mc_clean.replace("M", ""))
            return value * 1000
        elif "B" in mc_clean:
            value = float(mc_clean.replace("B", ""))
            return value * 1000000
        else:
            value = float(mc_clean)
            return value / 1000
    except (ValueError, AttributeError):
        return 0

def get_tmc_color(tmc_value_k):
    """Get color fill for T-MC based on value in thousands"""
    if 30 <= tmc_value_k <= 60:
        return PatternFill(start_color="FFFF00", end_color="FFFF00", fill_type="solid")
    elif 61 <= tmc_value_k <= 100:
        return PatternFill(start_color="90EE90", end_color="90EE90", fill_type="solid")
    elif tmc_value_k >= 101:
        return PatternFill(start_color="ADD8E6", end_color="ADD8E6", fill_type="solid")
    else:
        return None

# 🔄 AUTOMATED RESCANNING FUNCTIONS
async def extract_tmc_from_response(response_text):
    """Extract only T-MC data from scanner response for rescanning"""
    t_mc_match = re.search(r"🔝 __([^_]+)__", response_text)
    if t_mc_match:
        return t_mc_match.group(1).strip()
    return "N/A"

async def rescan_token_for_tmc(token_address, interval_name):
    """Rescan a specific token and extract only T-MC data"""
    try:
        await client.send_message(DESTINATION_BOT, token_address)
        logger.info(f"🔄 RESCAN {interval_name}: Sent {token_address} for rescanning")

        await asyncio.sleep(5)

        for attempt in range(3):
            dest_messages = await client.get_messages(DESTINATION_BOT, limit=20)
            if dest_messages:
                for msg in dest_messages:
                    if msg.text and (
                        token_address in msg.text or
                        (len(token_address) > 10 and token_address[:10] in msg.text) or
                        any(indicator in msg.text for indicator in ["🔝", "T-MC", "__"])
                    ):
                        tmc_value = await extract_tmc_from_response(msg.text)
                        logger.info(f"🔄 RESCAN {interval_name}: Found T-MC {tmc_value} for {token_address}")
                        return tmc_value

            if attempt < 2:
                await asyncio.sleep(2)

        logger.warning(f"🔄 RESCAN {interval_name}: No response for {token_address}")
        return "N/A"

    except Exception as e:
        logger.error(f"🔄 RESCAN {interval_name}: Error rescanning {token_address}: {e}")
        return "N/A"

async def update_rescan_tmc_in_excel(token_address, interval_name, tmc_value):
    """Update specific T-MC column in Excel for rescanned token"""
    try:
        async with processing_lock:
            wb = load_workbook(EXCEL_FILE)
            ws = wb.active

            token_row = None
            for row_idx, row in enumerate(ws.iter_rows(min_row=2, values_only=True), start=2):
                if row and len(row) > 1 and row[1] == token_address:
                    token_row = row_idx
                    break

            if not token_row:
                logger.warning(f"🔄 RESCAN {interval_name}: Token {token_address} not found in Excel")
                return

            headers = [cell.value for cell in ws[1]]
            column_name = f"{interval_name} T-MC"

            if column_name not in headers:
                logger.error(f"🔄 RESCAN {interval_name}: Column {column_name} not found in headers")
                return

            column_idx = headers.index(column_name) + 1
            ws.cell(row=token_row, column=column_idx, value=tmc_value)

            tmc_value_k = parse_market_cap_value(tmc_value)
            tmc_color = get_tmc_color(tmc_value_k)

            if tmc_color:
                cell = ws.cell(row=token_row, column=column_idx)
                cell.fill = tmc_color
                logger.info(f"🎨 RESCAN {interval_name}: Applied color to {tmc_value} (${tmc_value_k}K)")

            wb.save(EXCEL_FILE)
            logger.info(f"✅ RESCAN {interval_name}: Updated {token_address} with T-MC {tmc_value}")

    except Exception as e:
        logger.error(f"🔄 RESCAN {interval_name}: Error updating Excel for {token_address}: {e}")

async def automated_rescanning_manager():
    """Background task to manage automated token rescanning"""
    logger.info("🔄 AUTOMATED RESCANNING: Manager started")

    while rescanning_active:
        try:
            current_time = time.time()
            tokens_to_rescan = []

            for token_address, initial_scan_time in list(token_timestamps.items()):
                time_elapsed = current_time - initial_scan_time

                for interval_name, interval_seconds in rescan_intervals.items():
                    rescan_key = f"{token_address}_{interval_name}"

                    if not hasattr(automated_rescanning_manager, 'completed_rescans'):
                        automated_rescanning_manager.completed_rescans = set()

                    if (time_elapsed >= interval_seconds and
                        rescan_key not in getattr(automated_rescanning_manager, 'completed_rescans', set())):
                        tokens_to_rescan.append((token_address, interval_name))
                        getattr(automated_rescanning_manager, 'completed_rescans', set()).add(rescan_key)

            for token_address, interval_name in tokens_to_rescan:
                logger.info(f"🔄 AUTOMATED RESCANNING: Processing {interval_name} rescan for {token_address}")

                tmc_value = await rescan_token_for_tmc(token_address, interval_name)
                await update_rescan_tmc_in_excel(token_address, interval_name, tmc_value)

                await asyncio.sleep(2)

            await asyncio.sleep(60)

        except Exception as e:
            logger.error(f"🔄 AUTOMATED RESCANNING: Manager error: {e}")
            await asyncio.sleep(60)

async def export_to_excel(data):
    """Export data to Excel with thread-safety."""
    async with processing_lock:
        try:
            wb = load_workbook(EXCEL_FILE)
            ws = wb.active
            token_exists = False
            for row_idx, row in enumerate(ws.iter_rows(min_row=2, values_only=True), start=2):
                if row[1] == data.get("Token Address"):
                    token_exists = True
                    logger.info(f"⏩ Token {data['Token Address']} already exists, skipping")
                    return
            if not token_exists:
                morocco_time = datetime.now(MOROCCO_TZ).strftime("%Y-%m-%d %H:%M:%S")
                row = [morocco_time, data["Token Address"], data["Token Name"], data["Warnings"], data["Age"], data["Source Age"], data["MC"], data["T-MC"], "N/A", "N/A", "N/A", data["Liq"], data["Liq SOL"], data["First 20 Percentage"], data["First 20"], data["Whale Fish Pattern"], data["Made"], data["Volume"], data["Price"], data["Scans"], data["Hodls"], data["Top Holders"], data["Snipers"], data["Snipers Percentage"], data["Dex Paid"], data["High"], data["Dev"], data["LP Burnt"], data["Bundled"], data["Airdrop"], data["Burnt"], data["Sold"], data["Bond"]]
                ws.append(row)

                # 🔄 REGISTER TOKEN FOR AUTOMATED RESCANNING
                token_timestamps[data["Token Address"]] = time.time()
                logger.info(f"🔄 REGISTERED for rescanning: {data['Token Address']}")

                current_row = ws.max_row
                tmc_column_index = 8
                tmc_cell = ws.cell(row=current_row, column=tmc_column_index)
                tmc_value_k = parse_market_cap_value(data["T-MC"])
                tmc_color = get_tmc_color(tmc_value_k)
                if tmc_color:
                    tmc_cell.fill = tmc_color
                    logger.info(f"🎨 Applied color to T-MC: {data['T-MC']} (${tmc_value_k}K)")
                logger.info(f"✅ Added new row for token {data['Token Address']}")
            wb.save(EXCEL_FILE)
            logger.info(f"✅ Data saved to {EXCEL_FILE}")
        except Exception as e:
            logger.error(f"❌ Failed to export data to Excel: {e}")

async def forward_token(token_address):
    """Forward the token address to the destination."""
    try:
        await client.send_message(DESTINATION_BOT, token_address)
        logger.info(f"✅ Forwarded token to destination: {token_address}")
    except Exception as e:
        logger.error(f"❌ Failed to forward token: {e}")

async def process_one_message():
    """Fetch and process one message at a time."""
    global last_processed_message_id
    async with processing_lock:
        messages = await client.get_messages(SOURCE_USERNAME, limit=1)
        if not messages:
            logger.info("⚠️ No new messages found.")
            return
        message = messages[0]
        if last_processed_message_id and message.id <= last_processed_message_id:
            logger.info("⚠️ No new messages to process.")
            return
        last_processed_message_id = message.id
        message_text = message.message
        if message_text is None:
            logger.info("⚠️ Message contains no text. Skipping...")
            return
        logger.info(f"📩 New message from {SOURCE_USERNAME}: {message_text}")
        token_address = extract_token_from_text(message_text)
        # Source Age extraction removed
        if not token_address:
            logger.info("⚠️ No token found in the message.")
            return
        if token_address in processed_tokens:
            logger.info(f"⚠️ Token already processed: {token_address}")
            return
        message_text_lower = message_text.lower()
        if WHITELIST and not any(word.lower() in message_text_lower for word in WHITELIST):
            logger.info("⚠️ Message does not contain whitelist keywords.")
            return
        if BLACKLIST and any(word.lower() in message_text_lower for word in BLACKLIST):
            logger.info("⚠️ Message contains blacklist keywords.")
            return
        processed_tokens.add(token_address)
        logger.info(f"🎯 Found token: {token_address}")
        await forward_token(token_address)
        logger.info(f"⚡ HYBRID: Sent token, waiting 5s for complete response...")

        # 🎯 HYBRID: Wait 5 seconds for bot to process completely
        await asyncio.sleep(5)  # Give bot proper time to process and respond with complete data

        # Enhanced response detection with multiple attempts
        response = None
        for attempt in range(3):  # 3 attempts over 6 additional seconds
            dest_messages = await client.get_messages(DESTINATION_BOT, limit=20)  # Get more messages
            if dest_messages:
                # Look for a message that contains our token or scanner data
                for msg in dest_messages:
                    if msg.text and (
                        token_address in msg.text or
                        (len(token_address) > 10 and token_address[:10] in msg.text) or
                        any(indicator in msg.text for indicator in ["**$", "MC:", "Age:", "Scans:", "💰", "🕒"])
                    ):
                        response = msg
                        total_time = 5 + (attempt + 1) * 2
                        logger.info(f"🔍 HYBRID: Found complete response in {total_time}s for token {token_address}")
                        break

                if response:
                    break
                else:
                    if attempt < 2:  # Don't wait on last attempt
                        logger.info(f"⏳ No response found, waiting 2 more seconds... (attempt {attempt + 1}/3)")
                        await asyncio.sleep(2)

        if response:
            logger.info(f"📩 Destination bot response: {response.text[:200]}...")
            combined_data = extract_data_from_scanner_response(response.text, token_address)
            await export_to_excel(combined_data)
            logger.info("✅ Data extracted and saved to Excel")
        else:
            logger.warning("⚠️ No valid response from destination bot after 3 attempts")
            # Still save the token with minimal data
            minimal_data = {
                "Token Address": token_address,
                "Token Name": "N/A",
                "Warnings": "N/A",
                "Age": "N/A",

            }
            await export_to_excel(minimal_data)
            logger.info("✅ Saved minimal token data to Excel")

        logger.info("🛑 Finished processing message.")

async def message_handler(event):
    """🚀 ULTRA-FAST message handler - ZERO DELAYS, INSTANT PROCESSING"""
    # NO PROCESSING LOCK - Allow parallel processing for MAXIMUM SPEED
    # NO DELAYS - Process immediately

    message = event.message
    message_text = message.message

    # INSTANT skip check
    if not message_text:
        return

    # INSTANT filtering
    message_text_lower = message_text.lower()
    if WHITELIST and not any(word.lower() in message_text_lower for word in WHITELIST):
        return
    if BLACKLIST and any(word.lower() in message_text_lower for word in BLACKLIST):
        return

    # INSTANT queuing - NO DELAYS
    try:
        await message_queue.put((time.time(), message))
        stats["queued"] += 1
        logger.info(f"⚡ ULTRA-FAST QUEUED: {message_text[:40]}... | Queue: {message_queue.qsize()}")
    except asyncio.QueueFull:
        logger.warning("🚨 Queue full! Processing at MAXIMUM SPEED!")

# 🚀 ULTRA-FAST WORKER FUNCTIONS
async def ultra_fast_worker(worker_id):
    """🚀 GENIUS: Ultra-fast parallel worker for MAXIMUM SPEED"""
    logger.info(f"🚀 ULTRA-FAST-WORKER-{worker_id}: Started")

    while True:
        try:
            # Get message from queue INSTANTLY
            timestamp, message = await message_queue.get()
            queue_time = time.time() - timestamp

            logger.info(f"⚡ WORKER-{worker_id}: Processing (queued {queue_time:.2f}s)")

            # ULTRA-FAST processing
            await process_message_ultra_fast(message, worker_id)

            # Mark task done
            message_queue.task_done()
            stats["processed"] += 1

        except Exception as e:
            logger.error(f"❌ WORKER-{worker_id}: {e}")
            await asyncio.sleep(0.01)  # Minimal recovery delay

async def process_message_ultra_fast(message, worker_id):
    """🚀 ULTRA-FAST message processing with ZERO unnecessary delays"""
    message_text = message.message

    # INSTANT token extraction
    token_address = extract_token_from_text(message_text)
    if not token_address:
        return

    # SPEED CHECK: Skip duplicates instantly
    if token_address in processed_tokens:
        logger.info(f"⚡ WORKER-{worker_id}: Already processed: {token_address[:10]}...")
        return

    # Extract source age instantly
    source_age = extract_source_age(message_text)

    # Mark processed IMMEDIATELY
    processed_tokens.add(token_address)
    logger.info(f"🎯 WORKER-{worker_id}: NEW TOKEN: {token_address} | Age: {source_age}")

    # INSTANT bot communication
    try:
        await client.send_message(DESTINATION_BOT, token_address)
        logger.info(f"⚡ WORKER-{worker_id}: Sent to bot INSTANTLY")

        # GENIUS: Start ultra-fast response monitoring
        response_data = await ultra_fast_response_monitor(token_address, worker_id, source_age)

        # INSTANT Excel save
        await export_to_excel(response_data)
        logger.info(f"✅ WORKER-{worker_id}: Excel updated INSTANTLY")

    except Exception as e:
        logger.error(f"❌ WORKER-{worker_id}: Error: {e}")

async def ultra_fast_response_monitor(token_address, worker_id, source_age):
    """🚀 HYBRID: INSTANT detection + 5s timeout for complete bot response"""
    # 🎯 HYBRID: Wait 5 seconds for bot to process completely
    await asyncio.sleep(5)  # Give bot proper time to process and respond with complete data
    logger.info(f"⏳ WORKER-{worker_id}: Waited 5s, now checking for complete response...")

    # Enhanced response detection with multiple attempts
    for attempt in range(3):  # 3 attempts over 6 additional seconds
        try:
            # Check for complete response
            messages = await client.get_messages(DESTINATION_BOT, limit=20)

            for msg in messages:
                if msg.text and (
                    token_address in msg.text or
                    (len(token_address) > 10 and token_address[:10] in msg.text) or
                    any(indicator in msg.text for indicator in ["**$", "MC:", "Age:", "Scans:"])
                ):
                    total_time = 5 + (attempt + 1) * 2
                    stats["responses"] += 1
                    logger.info(f"🔍 WORKER-{worker_id}: Found complete response in {total_time}s!")

                    # Extract complete data
                    return extract_data_from_scanner_response(msg.text, token_address, source_age)

        except Exception as e:
            logger.error(f"❌ WORKER-{worker_id}: Monitor error: {e}")

        # Wait 2 more seconds before next attempt (except on last attempt)
        if attempt < 2:
            logger.info(f"⏳ WORKER-{worker_id}: No response yet, waiting 2 more seconds... (attempt {attempt + 1}/3)")
            await asyncio.sleep(2)

    # Return minimal data if no response after 5s + 6s = 11s total
    logger.warning(f"⚠️ WORKER-{worker_id}: No response after 11s, saving minimal data")
    return {
        "Token Address": token_address,
        "Token Name": "N/A",
        "Warnings": "N/A",
        "Age": "N/A",
        "Source Age": source_age
    }

# 🚀 ULTRA-FAST PROCESSING COMPLETE - All processing now handled by parallel workers

async def print_ultra_fast_stats():
    """Print ULTRA-FAST performance statistics"""
    while True:
        await asyncio.sleep(30)  # Every 30 seconds
        logger.info(f"📊 ULTRA-FAST STATS: Processed:{stats['processed']} | Queued:{stats['queued']} | Responses:{stats['responses']} | Queue:{message_queue.qsize()}")

async def main():
    """Main function"""
    global client
    logger.info("🚀 Starting HYBRID ULTRA-FAST FDV1 Scanner with AUTOMATED RESCANNING")
    logger.info(f"📍 Source: {SOURCE_USERNAME}")
    logger.info(f"📍 Destination: {DESTINATION_BOT}")
    logger.info(f"📍 Workers: {WORKER_COUNT}")
    logger.info(f"📊 Excel File: {EXCEL_FILE}")
    logger.info("⚡ HYBRID MODE: INSTANT message detection + 5s bot timeout for complete data")
    logger.info("🔄 AUTOMATED RESCANNING: 1h, 5h, 24h intervals for T-MC updates")
    logger.info("=" * 80)
    initialize_excel()
    client = TelegramClient(SESSION_FILE.replace('.session', ''), API_ID, API_HASH)
    try:
        await client.start(phone=PHONE)
        logger.info("✅ Client started successfully")

        # Resolve the source channel entity
        try:
            source_entity = await client.get_entity(SOURCE_USERNAME)
            logger.info(f"✅ Resolved source channel: {source_entity.title} (ID: {source_entity.id})")
        except Exception as e:
            logger.error(f"❌ Failed to resolve source channel {SOURCE_USERNAME}: {e}")
            return

        # 🚀 START ULTRA-FAST WORKERS
        workers = []
        for i in range(WORKER_COUNT):
            worker = asyncio.create_task(ultra_fast_worker(i + 1))
            workers.append(worker)

        # Start stats printer
        asyncio.create_task(print_ultra_fast_stats())

        logger.info(f"🚀 Started {WORKER_COUNT} ULTRA-FAST workers")

        # Register the event handler for new messages
        client.add_event_handler(message_handler, events.NewMessage(chats=[source_entity]))
        logger.info("⚡ HYBRID LISTENING: INSTANT detection + 5s bot timeout for complete data!")

        # 🔄 START AUTOMATED RESCANNING MANAGER
        rescanning_task = asyncio.create_task(automated_rescanning_manager())
        logger.info("🔄 AUTOMATED RESCANNING: Background manager started")

        # Keep the client running to listen for events
        await client.run_until_disconnected()

    except Exception as e:
        logger.error(f"❌ Error in main: {e}")
    finally:
        if client:
            await client.disconnect()
            logger.info("🔌 Client disconnected")

if __name__ == "__main__":
    asyncio.run(main())
