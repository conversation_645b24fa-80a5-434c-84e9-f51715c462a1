"""
Scanner Data Recovery Script for Solana Token Tracker

This script takes a list of token addresses from tokens.txt and
searches soul_scanner_bot history to find the original first scan data.
"""

from telethon import TelegramClient
from telethon.tl.types import MessageEntityTextUrl, MessageEntityUrl
import re
import asyncio
import os
import time
from openpyxl import load_workbook
from datetime import datetime, timedelta
import logging
import sys

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Import functions from main script
from early import (
    extract_data_from_scanner_response,
    API_ID, 
    API_HASH, 
    PHONE, 
    destination_username,
    EXCEL_FILE,
    HEADERS
)

# Constants
MAX_MESSAGES = 10000  # Maximum number of messages to process (increased)
RATE_LIMIT = 0.05  # Faster rate limiting
HISTORY_DAYS = 90  # Look back further (3 months)
TOKENS_FILE = "tokens.txt"  # File containing tokens to search
CHUNK_SIZE = 2000  # Process messages in chunks to avoid memory limitations

async def update_excel_with_scanner_data(token_address, scanner_data):
    """Update Excel with original scanner data for the token"""
    try:
        if not scanner_data:
            logger.warning(f"No scanner data for {token_address}")
            return False
            
        wb = load_workbook(EXCEL_FILE)
        ws = wb.active
        
        # Find if token exists
        token_exists = False
        token_row = None
        
        for row_idx, row in enumerate(ws.iter_rows(min_row=2, values_only=True), start=2):
            if row[1] == token_address:  # Token Address is column 2
                token_exists = True
                token_row = row_idx
                break
        
        if token_exists:
            # Only update fields that are missing (N/A) or empty
            fields_updated = 0
            
            for field, value in scanner_data.items():
                if field in HEADERS and value != "N/A":
                    col_idx = HEADERS.index(field) + 1
                    current_value = ws.cell(row=token_row, column=col_idx).value
                    
                    # Only update if current value is missing or N/A
                    if not current_value or current_value == "N/A":
                        ws.cell(row=token_row, column=col_idx, value=value)
                        fields_updated += 1
            
            if fields_updated > 0:
                wb.save(EXCEL_FILE)
                logger.info(f"Updated {fields_updated} missing fields for token {token_address}")
                return True
            else:
                logger.info(f"No fields needed update for token {token_address}")
                return False
        else:
            # Token not in Excel, add a new row
            now = datetime.now()
            row = [now.strftime("%Y-%m-%d %H:%M:%S")]  # Timestamp
            
            # Add all fields in the correct order
            for field in HEADERS[1:]:  # Skip Timestamp
                if field in scanner_data:
                    row.append(scanner_data[field])
                else:
                    row.append("N/A")
            
            ws.append(row)
            wb.save(EXCEL_FILE)
            logger.info(f"Added new row for token {token_address}")
            return True
        
    except Exception as e:
        logger.error(f"Error updating Excel for {token_address}: {e}")
        return False

async def load_tokens_from_file():
    """Load token addresses from the tokens.txt file"""
    tokens = []
    
    if not os.path.exists(TOKENS_FILE):
        logger.error(f"Tokens file not found: {TOKENS_FILE}")
        return []
        
    with open(TOKENS_FILE, 'r') as f:
        for line in f:
            token = line.strip()
            if token and len(token) >= 43:  # Valid Solana addresses are 43+ chars
                tokens.append(token)
    
    logger.info(f"Loaded {len(tokens)} tokens from {TOKENS_FILE}")
    return tokens

async def get_scanner_bot_messages(client, cutoff_date, offset=0):
    """Get a batch of scanner bot messages with pagination"""
    logger.info(f"Fetching messages from {destination_username} (offset: {offset})")
    messages = await client.get_messages(
        destination_username,
        limit=CHUNK_SIZE,
        offset_date=cutoff_date if offset == 0 else None,
        offset_id=0 if offset == 0 else offset
    )
    return messages

async def find_original_scanner_data():
    """Find the original scanner data for tokens by searching scanner bot history"""
    # Connect to Telegram
    client = TelegramClient("scanner_data_recovery", API_ID, API_HASH)
    await client.start(PHONE)
    
    try:
        # Load tokens from file
        tokens = await load_tokens_from_file()
        if not tokens:
            logger.error("No tokens found in tokens file. Exiting.")
            return
            
        # Calculate date cutoff for scanner history
        cutoff_date = datetime.now() - timedelta(days=HISTORY_DAYS)
        
        # Match patterns used to identify scanner bot responses
        scanner_pattern = re.compile(r"\*\*Token Name\*\*")
        token_pattern = re.compile(r'\b([A-Za-z0-9]{43,44})\b')
        
        # First pass: extract all scanner bot responses and map to tokens
        logger.info(f"Starting to retrieve messages from {destination_username}")
        
        scanner_responses = []
        total_messages = 0
        last_id = 0
        
        # Retrieve messages in chunks to handle large message history
        while total_messages < MAX_MESSAGES:
            messages = await get_scanner_bot_messages(client, cutoff_date, last_id)
            
            if not messages or len(messages) == 0:
                logger.info("No more messages to retrieve")
                break
                
            last_id = messages[-1].id
            total_messages += len(messages)
            
            # Process this batch of messages
            batch_responses = []
            for msg in messages:
                if not hasattr(msg, 'text') or not msg.text:
                    continue
                    
                # Check if this is a scanner response 
                if scanner_pattern.search(msg.text) and msg.text.count("**") > 4:
                    # Extract potential token addresses from the message
                    found_tokens = token_pattern.findall(msg.text)
                    
                    # Store this scanner response with the date and potential tokens
                    batch_responses.append({
                        "text": msg.text,
                        "date": msg.date,
                        "tokens": found_tokens,
                        "id": msg.id
                    })
            
            scanner_responses.extend(batch_responses)
            logger.info(f"Retrieved {len(messages)} messages, found {len(batch_responses)} scanner responses in this batch")
            
            # Small delay between batches
            await asyncio.sleep(1.0)
        
        logger.info(f"Retrieved total {total_messages} messages, found {len(scanner_responses)} scanner responses")
        
        # Optimize token lookup with a token map for quick reference
        token_map = {token.lower(): token for token in tokens}
        
        # Process each token from our list
        tokens_processed = 0
        tokens_updated = 0
        
        for token in tokens:
            token_lower = token.lower()
            # Find all scanner responses that include this token
            matching_responses = []
            
            for resp in scanner_responses:
                token_match = False
                
                # Check token variations (with/without case sensitivity)
                if any(t.lower() == token_lower for t in resp["tokens"]):
                    token_match = True
                # Additional check - sometimes tokens are in text but not properly extracted
                elif token in resp["text"] or token_lower in resp["text"].lower():
                    token_match = True
                    
                if token_match:
                    matching_responses.append((resp["text"], resp["date"]))
            
            if matching_responses:
                # Sort by date (oldest first) to get the original scan
                matching_responses.sort(key=lambda x: x[1])
                oldest_message, oldest_date = matching_responses[0]
                
                # Log details for debugging
                logger.info(f"Found {len(matching_responses)} matches for token {token}, oldest from {oldest_date}")
                
                # Extract data from the message
                scanner_data = extract_data_from_scanner_response(oldest_message, token)
                
                # Update Excel
                if await update_excel_with_scanner_data(token, scanner_data):
                    tokens_updated += 1
                
                tokens_processed += 1
                
                # Rate limiting to avoid overloading
                await asyncio.sleep(RATE_LIMIT)
                
                # Progress updates
                if tokens_processed % 10 == 0:
                    logger.info(f"Progress: Processed {tokens_processed}/{len(tokens)} tokens")
            else:
                logger.warning(f"No scanner data found for token {token}")
                tokens_processed += 1
        
        logger.info(f"Completed processing {tokens_processed} tokens with original scanner data")
        logger.info(f"Successfully updated {tokens_updated} tokens in Excel")
        
    except Exception as e:
        logger.error(f"Error in finding original scanner data: {e}")
    finally:
        await client.disconnect()

if __name__ == "__main__":
    # Check if tokens file exists, if not create an example
    if not os.path.exists(TOKENS_FILE):
        logger.info(f"Creating example {TOKENS_FILE}")
        with open(TOKENS_FILE, 'w') as f:
            f.write("# Add one token address per line below\n")
            f.write("# Example:\n")
            f.write("# Ds4tRwgneY4Yj2gWtEsSNwhS2dsM411Apvf1geL5pump\n")
        logger.info(f"Please add token addresses to {TOKENS_FILE} and run again")
    else:
        # Run the recovery script
        asyncio.run(find_original_scanner_data())