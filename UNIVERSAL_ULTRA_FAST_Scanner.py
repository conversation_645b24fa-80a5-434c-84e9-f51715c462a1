"""
🚀 UNIVERSAL ULTRA-FAST SCANNER 🚀
GENIUS-LEVEL HIGH-SPEED TOKEN DETECTION & PROCESSING

Features:
- ZERO DELAYS: Instant message detection
- PARALLEL PROCESSING: 10 workers processing simultaneously  
- SMART QUEUING: Handles message bursts
- MULTI-SOURCE: Monitors ALL channels simultaneously
- ULTRA-FAST RESPONSE: 0.2s response checking intervals
- INTELLIGENT CACHING: Prevents duplicate processing
"""

from telethon import TelegramClient, events
import asyncio
import re
import logging
import os
import time
from datetime import datetime
import pytz
from openpyxl import Workbook, load_workbook
from openpyxl.styles import PatternFill
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# UNIVERSAL CONFIGURATION - Uses Early credentials as primary
API_ID = os.getenv("EARLY_API_ID")
API_HASH = os.getenv("EARLY_API_HASH") 
PHONE = os.getenv("EARLY_PHONE")
DESTINATION_BOT = "@soul_scanner_bot"

# MULTI-SOURCE MONITORING
SOURCES = {
    "early": "@solearlytrending",
    "ath": "@ATH_Solana_Alerts", 
    "solbix": "@solbix_alerts",
    "litaipump": "@LIT_AI_Pump_Fun_New_Tokens",
    "newpool": "@newpool_alerts",
    "fdv1": "@FDV_GMGN_Alerts",
    "fdv2": "@FDV_GMGN_Alerts_2",
    "pumpking": "@pump_king_alerts"
}

# GENIUS SETTINGS - MAXIMUM SPEED
WORKER_COUNT = 10  # 10 parallel workers!
MAX_QUEUE_SIZE = 2000  # Handle massive message bursts
RESPONSE_CHECK_INTERVAL = 0.2  # Check every 0.2 seconds!
MAX_RESPONSE_ATTEMPTS = 25  # 5 seconds total (25 * 0.2s)

# Global variables
client = None
processed_tokens = set()
message_queue = asyncio.Queue(maxsize=MAX_QUEUE_SIZE)
response_cache = {}
stats = {"processed": 0, "queued": 0, "responses": 0}

# Files
SESSION_FILE = "UNIVERSAL_ULTRA_FAST.session"
EXCEL_FILE = "UNIVERSAL_ULTRA_FAST_data.xlsx"

# Morocco timezone
MOROCCO_TZ = pytz.timezone('Africa/Casablanca')

# ULTRA-FAST logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Excel headers
HEADERS = ["Timestamp", "Source", "Token Address", "Token Name", "MC", "Age", "Scans", "Snipers", "Response Time"]

def initialize_excel():
    if not os.path.exists(EXCEL_FILE):
        wb = Workbook()
        ws = wb.active
        ws.append(HEADERS)
        wb.save(EXCEL_FILE)
        logger.info(f"🚀 Created UNIVERSAL ULTRA-FAST Excel: {EXCEL_FILE}")

def extract_token_universal(message_text):
    """GENIUS: Universal token extraction for ALL patterns"""
    patterns = [
        r"([A-Za-z0-9]{39,40}pump)",  # Pump tokens
        r"https://t\.me/soul_sniper_bot\?start=15_([A-Za-z0-9]{43,44})",  # Soul sniper
        r"https://www\.geckoterminal\.com/solana/pools/([A-Za-z0-9]{43,44})",  # Gecko
        r"https://gmgn\.ai/sol/token/([A-Za-z0-9]{43,44})",  # GMGN
        r"CA:\s*([A-Za-z0-9]{43,44})",  # CA pattern
        r"\b([A-Za-z0-9]{43,44})\b"  # Direct token
    ]
    
    for pattern in patterns:
        matches = re.findall(pattern, message_text)
        if matches:
            return matches[0]
    return None

def detect_source_type(chat_username):
    """Detect which source the message came from"""
    for source_name, source_username in SOURCES.items():
        if source_username.replace('@', '') == chat_username:
            return source_name
    return "unknown"

def should_process_message(message_text, source_type):
    """ULTRA-FAST filtering based on source type"""
    message_lower = message_text.lower()
    
    # Source-specific keywords
    keywords = {
        "early": ["new trending"],
        "ath": ["ath"],
        "solbix": ["solbix"],
        "litaipump": ["pump info"],
        "newpool": ["new pool"],
        "fdv1": ["fdv"],
        "fdv2": ["fdv"],
        "pumpking": ["pump king"]
    }
    
    # Check if message contains relevant keywords
    source_keywords = keywords.get(source_type, [])
    if source_keywords and not any(kw in message_lower for kw in source_keywords):
        return False
    
    # Universal blacklist
    blacklist = ["sellamount", "sell amount", "sold"]
    if any(word in message_lower for word in blacklist):
        return False
    
    return True

async def export_to_excel(data):
    """ULTRA-FAST Excel export with minimal locking"""
    try:
        wb = load_workbook(EXCEL_FILE)
        ws = wb.active
        
        timestamp = datetime.now(MOROCCO_TZ).strftime("%Y-%m-%d %H:%M:%S")
        
        ws.append([
            timestamp,
            data.get("Source", "N/A"),
            data.get("Token Address", "N/A"),
            data.get("Token Name", "N/A"),
            data.get("MC", "N/A"),
            data.get("Age", "N/A"),
            data.get("Scans", "N/A"),
            data.get("Snipers", "N/A"),
            data.get("Response Time", "N/A")
        ])
        
        wb.save(EXCEL_FILE)
        stats["processed"] += 1
        
    except Exception as e:
        logger.error(f"❌ Excel error: {e}")

async def universal_message_handler(event):
    """GENIUS: Universal message handler for ALL sources"""
    message = event.message
    message_text = message.message

    if not message_text:
        return

    # Detect source
    try:
        chat = await event.get_chat()
        chat_username = chat.username if hasattr(chat, 'username') else str(chat.id)
        source_type = detect_source_type(chat_username)
    except:
        source_type = "unknown"

    # INSTANT filtering
    if not should_process_message(message_text, source_type):
        return

    # INSTANT queuing
    try:
        await message_queue.put((time.time(), message, source_type))
        stats["queued"] += 1
        logger.info(f"⚡ QUEUED [{source_type.upper()}]: {message_text[:40]}... | Q:{message_queue.qsize()}")
    except asyncio.QueueFull:
        logger.warning(f"🚨 Queue full! Source: {source_type}")

async def ultra_fast_universal_worker(worker_id):
    """GENIUS: Universal ultra-fast worker"""
    logger.info(f"🚀 UNIVERSAL-WORKER-{worker_id}: Started")
    
    while True:
        try:
            timestamp, message, source_type = await message_queue.get()
            queue_time = time.time() - timestamp
            
            logger.info(f"⚡ WORKER-{worker_id} [{source_type.upper()}]: Processing (queued {queue_time:.2f}s)")
            
            await process_universal_message(message, source_type, worker_id)
            message_queue.task_done()
            
        except Exception as e:
            logger.error(f"❌ WORKER-{worker_id}: {e}")
            await asyncio.sleep(0.01)

async def process_universal_message(message, source_type, worker_id):
    """ULTRA-FAST universal message processing"""
    message_text = message.message
    
    # INSTANT token extraction
    token_address = extract_token_universal(message_text)
    if not token_address:
        return

    # SPEED CHECK: Skip duplicates
    if token_address in processed_tokens:
        return

    # Mark processed IMMEDIATELY
    processed_tokens.add(token_address)
    
    logger.info(f"🎯 WORKER-{worker_id} [{source_type.upper()}]: NEW TOKEN: {token_address}")

    # INSTANT bot communication
    start_time = time.time()
    try:
        await client.send_message(DESTINATION_BOT, token_address)
        logger.info(f"⚡ WORKER-{worker_id}: Sent to bot INSTANTLY")
        
        # GENIUS: Start ultra-fast response monitoring
        response_data = await ultra_fast_universal_response_monitor(token_address, worker_id, start_time)
        response_data["Source"] = source_type
        
        # INSTANT Excel save
        await export_to_excel(response_data)
        
    except Exception as e:
        logger.error(f"❌ WORKER-{worker_id}: Error: {e}")

async def ultra_fast_universal_response_monitor(token_address, worker_id, start_time):
    """GENIUS: Ultra-fast response monitoring - 0.2s intervals!"""
    for attempt in range(MAX_RESPONSE_ATTEMPTS):
        await asyncio.sleep(RESPONSE_CHECK_INTERVAL)
        
        try:
            messages = await client.get_messages(DESTINATION_BOT, limit=25)
            
            for msg in messages:
                if msg.text and (
                    token_address in msg.text or
                    (len(token_address) > 8 and token_address[:8] in msg.text) or
                    any(indicator in msg.text for indicator in ["**$", "MC:", "Age:", "Scans:"])
                ):
                    response_time = time.time() - start_time
                    stats["responses"] += 1
                    
                    logger.info(f"🔍 WORKER-{worker_id}: RESPONSE in {response_time:.2f}s!")
                    
                    # ULTRA-FAST data extraction
                    data = extract_scanner_data_ultra_fast(msg.text, token_address, response_time)
                    return data
                    
        except Exception as e:
            logger.error(f"❌ WORKER-{worker_id}: Monitor error: {e}")
    
    # Return minimal data if no response
    return {
        "Token Address": token_address,
        "Token Name": "N/A",
        "MC": "N/A", 
        "Age": "N/A",
        "Scans": "N/A",
        "Snipers": "N/A",
        "Response Time": "No Response"
    }

def extract_scanner_data_ultra_fast(response_text, token_address, response_time):
    """ULTRA-FAST data extraction from scanner response"""
    data = {
        "Token Address": token_address,
        "Response Time": f"{response_time:.2f}s",
        "Token Name": "N/A",
        "MC": "N/A",
        "Age": "N/A", 
        "Scans": "N/A",
        "Snipers": "N/A"
    }
    
    # Quick regex extractions
    patterns = {
        "Token Name": r"\*\*\$([A-Za-z0-9]+)\*\*",
        "MC": r"MC:\s*\$?([0-9,.]+[KMB]?)",
        "Age": r"Age:\s*([^🕒\n]+)",
        "Scans": r"Scans:\s*(\d+)",
        "Snipers": r"Snipers:\s*(\d+)"
    }
    
    for key, pattern in patterns.items():
        match = re.search(pattern, response_text)
        if match:
            data[key] = match.group(1).strip()
    
    return data

async def print_stats():
    """Print performance statistics"""
    while True:
        await asyncio.sleep(30)  # Every 30 seconds
        logger.info(f"📊 STATS: Processed:{stats['processed']} | Queued:{stats['queued']} | Responses:{stats['responses']} | Queue:{message_queue.qsize()}")

async def main():
    """GENIUS: Universal ultra-fast main function"""
    global client
    
    logger.info("🚀 STARTING UNIVERSAL ULTRA-FAST SCANNER")
    logger.info(f"📍 Sources: {len(SOURCES)} channels")
    logger.info(f"📍 Workers: {WORKER_COUNT}")
    logger.info(f"📍 Response Check: Every {RESPONSE_CHECK_INTERVAL}s")
    logger.info(f"📊 Excel: {EXCEL_FILE}")
    logger.info("=" * 60)

    initialize_excel()
    client = TelegramClient(SESSION_FILE.replace('.session', ''), API_ID, API_HASH)

    try:
        await client.start(phone=PHONE)
        logger.info("✅ Client started ULTRA-FAST")

        # Resolve ALL sources
        source_entities = []
        for source_name, source_username in SOURCES.items():
            try:
                entity = await client.get_entity(source_username)
                source_entities.append(entity)
                logger.info(f"✅ {source_name.upper()}: {entity.title}")
            except Exception as e:
                logger.error(f"❌ Failed to resolve {source_name}: {e}")

        # Start workers
        workers = []
        for i in range(WORKER_COUNT):
            worker = asyncio.create_task(ultra_fast_universal_worker(i + 1))
            workers.append(worker)
        
        # Start stats printer
        asyncio.create_task(print_stats())
        
        logger.info(f"🚀 Started {WORKER_COUNT} UNIVERSAL ULTRA-FAST workers")

        # Register event handler for ALL sources
        client.add_event_handler(universal_message_handler, events.NewMessage(chats=source_entities))
        logger.info("⚡ UNIVERSAL LISTENING: MAXIMUM SPEED ACTIVATED!")

        # Keep running
        await client.run_until_disconnected()

    except Exception as e:
        logger.error(f"❌ Error: {e}")
    finally:
        if client:
            await client.disconnect()

if __name__ == "__main__":
    asyncio.run(main())
