from telethon import Telegram<PERSON>lient, events
from telethon.tl.types import MessageEntityTextUrl, MessageEntityUrl
import re
import asyncio
import os
from openpyxl import Workbook, load_workbook
from datetime import datetime

# Step 1: Replace these with your own credentials
api_id = "18004781"  # Replace with your API ID
api_hash = "0e6f3a64a6fc53a4c055d22a1e5eb88a"  # Replace with your API Hash
phone_number = "+212774259728"  # Replace with your phone number (e.g., "+1234567890")

# Step 2: Source and Destination IDs
source_channel_id = -1002122751413  # Replace with your channel ID (must include the -100 prefix)
destination_username = "@soul_scanner_bot"  # Username of the destination bot

# Step 3: Regex patterns to extract token from URLs and text
token_pattern = r"https://gmgn\.ai/sol/token/([A-Za-z0-9]{43,44})"  # Extracts token from URL
token_pattern_pump = r"([A-Za-z0-9]{39,40}pump)"  # Pattern for tokens ending with "pump"

# Step 4: Whitelist and Blacklist
whitelist = ["NewPool"]  # Process messages containing these words
blacklist = ["SellAmount"]  # Ignore messages containing these words

# Step 5: Rate Limiting (in seconds)
RATE_LIMIT_DELAY = 5  # 5-second delay between token transfers

# Step 6: Initialize the Telegram client
client = TelegramClient("newpool_data", api_id, api_hash)  # Changed session name to "SolSheet_bot"

# Step 7: Excel file setup
EXCEL_FILE = "newpool_data.xlsx"  # Name of the Excel file
if not os.path.exists(EXCEL_FILE):
    # Create a new Excel file with headers if it doesn't exist
    wb = Workbook()
    ws = wb.active
    ws.append(["Timestamp", "Token Address", "Token Name", "Warnings", "Age", "MC", "T-MC", "Liq", "Liq SOL", "Vol-1h", "Price-1h", "Scans", "Hodls", "High", "Snipers", "Snipers Percentage", "Dev", "Sniped", "Top Holders", "Dex", "LP", "First", "First Percentage", "Sold", "Burnt"])  # Add headers
    wb.save(EXCEL_FILE)

# Step 8: Function to clear the terminal
def clear_terminal():
    """Clear the terminal screen."""
    os.system("cls" if os.name == "nt" else "clear")

# Step 9: Set to track processed tokens
processed_tokens = set()

# Step 10: Lock to ensure sequential processing
processing_lock = asyncio.Lock()

# Step 11: Function to forward token to destination
async def forward_token(token_address):
    """Forward the token address to the destination."""
    try:
        await client.send_message(destination_username, token_address)
        print(f"✅ Forwarded token to destination: {token_address}")
    except Exception as e:
        print(f"❌ Failed to forward token: {e}")

# Step 12: Function to extract data from the message
def extract_data(message_text, token_address):
    """Extract data from the message text."""
    data = {
        "Token Address": token_address,  # Use the token address extracted from @solearlytrending
        "Token Name": None,
        "Warnings": "N/A",
        "Age": "N/A",
        "MC": "N/A",
        "T-MC": "N/A",  # New column for T-MC
        "Liq": "N/A",
        "Liq SOL": "N/A",  # New column for Liq SOL
        "Vol-1h": "N/A",  # New column for Vol-1h
        "Price-1h": "N/A",  # New column for Price-1h
        "Scans": "N/A",
        "Hodls": "N/A",  # New column for Hodls
        "High": "N/A",  # New column for High percentage
        "Snipers": "N/A",
        "Snipers Percentage": "N/A",
        "Dev": "N/A",
        "Sniped": "N/A",
        "Top Holders": "N/A",  # New column for Top Holders
        "Dex": "N/A",  # New column for Dex
        "LP": "N/A",  # New column for LP
        "First": "N/A",  # New column for First
        "First Percentage": "N/A",  # New column for First Percentage
        "Sold": "N/A",
        "Burnt": "N/A",
    }

    # Extract Token Name
    token_name_match = re.search(r"\*\*\$([A-Za-z0-9]+)\*\*", message_text)
    if token_name_match:
        data["Token Name"] = token_name_match.group(1)

    # Extract Warnings (including icons and text between double underscores)
    warnings = re.findall(r"(🚨|⚠️)?\s*__([^__]+)__", message_text)
    if warnings:
        # Filter warnings based on specific keywords
        filtered_warnings = [
            f"{icon} {warning}" if icon else warning
            for icon, warning in warnings
            if any(keyword.lower() in warning.lower() for keyword in ["High", "Migrating", "Low", "Dev", "Sniped", "Abnormal"])
        ]
        if filtered_warnings:
            data["Warnings"] = " | ".join(filtered_warnings)

    # Extract Age
    age_match = re.search(r"🕒 \*\*Age:\*\* (\d+[smhdw]+\s*\d*[smhdw]*)", message_text)
    if age_match:
        data["Age"] = age_match.group(1)

    # Extract MC
    mc_match = re.search(r"💰 \*\*MC:\*\* (\$\d+\.?\d*[KMB]?)", message_text)
    if mc_match:
        data["MC"] = mc_match.group(1)

    # Extract T-MC (data after 🔝 __)
    t_mc_match = re.search(r"🔝 __(\$\d+\.?\d*[KMB]?)__", message_text)
    if t_mc_match:
        data["T-MC"] = t_mc_match.group(1)

    # Extract Liq or vLiq and Liq SOL
    liq_match = re.search(r"💧 \*\*(v?Liq):\*\* (\$\d+\.?\d*[KMB]?) \((\d+\.?\d* SOL)\)", message_text)
    if liq_match:
        liq_type = liq_match.group(1)  # This will be either "Liq" or "vLiq"
        liq_value = liq_match.group(2)  # This will be the value like $9.7K, $13K, etc.
        liq_sol = liq_match.group(3)    # This will be the SOL value like 23 SOL, 0.2 SOL, etc.
        
        # Append "-v" if it's vLiq
        if liq_type == "vLiq":
            liq_value += "-v"
        
        data["Liq"] = liq_value
        data["Liq SOL"] = liq_sol

    # Extract Vol-1h (data after **Vol:** __1h__:)
    vol_1h_match = re.search(r"\*\*Vol:\*\* __1h__: (\$\d+\.?\d*[KMB]?)", message_text)
    if vol_1h_match:
        data["Vol-1h"] = vol_1h_match.group(1)

    # Extract Price-1h (data after **Price:** __1h__:)
    price_1h_match = re.search(r"\*\*Price:\*\* __1h__: ([-\d\.]+%)", message_text)
    if price_1h_match:
        data["Price-1h"] = price_1h_match.group(1)

    # Extract Scans
    scans_match = re.search(r"⚡ \*\*Scans:\*\* (\d+)", message_text)
    if scans_match:
        data["Scans"] = scans_match.group(1)

    # Extract Hodls
    hodls_match = re.search(r"\[\*\*Hodls\*\*\]\([^)]+\): (\d+)", message_text)
    if hodls_match:
        data["Hodls"] = hodls_match.group(1)

    # Extract High (percentage after "┗ High:")
    high_match = re.search(r"┗ High:\s*\[(\d+\.?\d*%)]", message_text)
    if high_match:
        data["High"] = high_match.group(1)  # Extract the percentage

    # Extract Snipers and Snipers Percentage
    snipers_match = re.search(r"🔫 \*\*Snipers:\*\* (\d+) • (\d+\.?\d*%)", message_text)
    if snipers_match:
        data["Snipers"] = snipers_match.group(1)
        data["Snipers Percentage"] = snipers_match.group(2)

    # Extract Dev
    dev_match = re.search(r"🛠️ Dev.*?(\d+ SOL)", message_text)
    if dev_match:
        data["Dev"] = dev_match.group(1)

    # Extract Sniped
    sniped_match = re.search(r"┣ Sniped: (\d+\.?\d*%)", message_text)
    if sniped_match:
        data["Sniped"] = sniped_match.group(1)

    # Extract Top Holders
    top_holders_match = re.search(r"\[\*\*Hodls\*\*\]\([^)]+\): \d+ • Top: (\d+\.?\d*%)", message_text)
    if top_holders_match:
        data["Top Holders"] = top_holders_match.group(1)

    # Extract Dex (data after "**:** Paid")
    dex_match = re.search(r"\[\*\*Dex\*\*\]\([^)]+\)\*\*:\*\* Paid(.+)", message_text)
    if dex_match:
        data["Dex"] = dex_match.group(1).strip()

    # Extract LP
    lp_match = re.search(r"\*\*:\*\* (\d+%) Burnt", message_text)
    if lp_match:
        data["LP"] = lp_match.group(1)

    # Extract First
    first_match = re.search(r"\*\*:\*\* (\d+)\s*Fresh", message_text)
    if first_match:
        data["First"] = first_match.group(1)

    # Extract First Percentage (percentage after "Fresh •")
    first_percentage_match = re.search(r"Fresh\s*•\s*(\d+%)", message_text)
    if first_percentage_match:
        data["First Percentage"] = first_percentage_match.group(1)

    # Extract Sold (percentage after "| Sold:")
    sold_match = re.search(r"\| Sold:\s*(\d+\.?\d*%)", message_text)
    if sold_match:
        data["Sold"] = sold_match.group(1)

    # Extract Burnt (percentage after "| Burnt:")
    burnt_match = re.search(r"\| Burnt:\s*(\d+\.?\d*%)", message_text)
    if burnt_match:
        data["Burnt"] = burnt_match.group(1)

    return data

# Step 13: Function to export data to Excel
def export_to_excel(data):
    """Export message data to an Excel file."""
    try:
        # Open the Excel file
        wb = load_workbook(EXCEL_FILE)
        ws = wb.active

        # Append the data to the Excel file
        ws.append([
            datetime.now().strftime("%Y-%m-%d %H:%M:%S"),  # Current timestamp
            data["Token Address"],
            data["Token Name"],
            data["Warnings"],
            data["Age"],
            data["MC"],
            data["T-MC"],  # New column for T-MC
            data["Liq"],
            data["Liq SOL"],  # New column for Liq SOL
            data["Vol-1h"],  # New column for Vol-1h
            data["Price-1h"],  # New column for Price-1h
            data["Scans"],
            data["Hodls"],  # New column for Hodls
            data["High"],  # New column for High percentage
            data["Snipers"],
            data["Snipers Percentage"],
            data["Dev"],
            data["Sniped"],
            data["Top Holders"],  # New column for Top Holders
            data["Dex"],  # New column for Dex
            data["LP"],  # New column for LP
            data["First"],  # New column for First
            data["First Percentage"],  # New column for First Percentage
            data["Sold"],  # New column for Sold
            data["Burnt"],  # New column for Burnt
        ])

        # Save the Excel file
        wb.save(EXCEL_FILE)
        print(f"✅ Data exported to {EXCEL_FILE}")
    except Exception as e:
        print(f"❌ Failed to export data to Excel: {e}")

# Step 14: Function to extract URLs from message entities
def extract_urls_from_entities(message):
    """Extract URLs from message entities (e.g., anchor text)."""
    urls = []
    if message.entities:
        for entity in message.entities:
            if isinstance(entity, (MessageEntityTextUrl, MessageEntityUrl)):  # Combine checks
                url = entity.url if isinstance(entity, MessageEntityTextUrl) else message.text[entity.offset:entity.offset + entity.length]
                urls.append(url)
    return urls

# Step 15: Function to extract token from URL or text
def extract_token_from_url(url):
    """Extract the token address from a URL using regex."""
    token_matches = re.findall(token_pattern, url)
    if token_matches:
        return token_matches[0]  # Return the first match
    return None

def extract_token_from_text(text):
    """Extract token from plain text (last 4 characters must be 'pump')."""
    token_matches = re.findall(token_pattern_pump, text)
    if token_matches:
        return token_matches[0]
    return None

# Step 16: Event handler for new messages
@client.on(events.NewMessage(chats=[source_channel_id]))  # Listen to messages from the source channel ID
async def message_handler(event):
    """Handle new messages in the specified channel."""
    async with processing_lock:  # Ensure only one token is processed at a time
        # Clear the terminal
        clear_terminal()

        # Rate limiting: Add a delay between processing messages
        await asyncio.sleep(RATE_LIMIT_DELAY)

        # Get the message text
        message_text = event.message.message
        print(f"📩 New message received: {message_text}")  # Debug log

        # Convert message text to lowercase for case-insensitive matching
        message_text_lower = message_text.lower()

        # Step 17: Whitelist check (process if any whitelist keyword is found)
        if whitelist and not any(word.lower() in message_text_lower for word in whitelist):
            print("⚠️ Message does not contain whitelist keywords.")
            return

        # Step 18: Blacklist check (skip if any blacklist keyword is found)
        if blacklist and any(word.lower() in message_text_lower for word in blacklist):
            print("⚠️ Message contains blacklist keywords.")
            return

        # Step 19: First check for tokens ending with "pump" in message text
        token_address = extract_token_from_text(message_text)
        if token_address:
            print(f"🎯 Found token ending with 'pump' in message: {token_address}")
            # Forward the token to the destination
            await forward_token(token_address)

            # Wait for 5 seconds to allow @soul_scanner_bot to process the token
            print("⏳ Waiting for 5 seconds...")
            await asyncio.sleep(5)

            # Fetch the last message from @soul_scanner_bot
            messages = await client.get_messages(destination_username, limit=1)
            if messages:
                last_message = messages[0]
                print(f"📩 Last message from @soul_scanner_bot: {last_message.text}")

                # Extract data from the last message
                data = extract_data(last_message.text, token_address)

                # Export data to Excel
                export_to_excel(data)
            else:
                print("⚠️ No messages found in @soul_scanner_bot.")
            return  # Exit after processing pump token

        # Step 20: Extract URLs from message entities (anchor text)
        urls = extract_urls_from_entities(event.message)
        if urls:
            print(f"🔗 Found URLs in message: {urls}")  # Debug log

            # Step 21: Extract tokens from URLs
            for url in urls:
                # Check if the URL matches the specific pattern
                if "https://gmgn.ai/sol/token/" in url:
                    token_address = extract_token_from_url(url)
                    if token_address:
                        print(f"🎯 Found token in URL: {token_address}")
                        # Forward the token to the destination
                        await forward_token(token_address)

                        # Wait for 5 seconds to allow @soul_scanner_bot to process the token
                        print("⏳ Waiting for 5 seconds...")
                        await asyncio.sleep(5)

                        # Fetch the last message from @soul_scanner_bot
                        messages = await client.get_messages(destination_username, limit=1)
                        if messages:
                            last_message = messages[0]
                            print(f"📩 Last message from @soul_scanner_bot: {last_message.text}")

                            # Extract data from the last message
                            data = extract_data(last_message.text, token_address)

                            # Export data to Excel
                            export_to_excel(data)
                        else:
                            print("⚠️ No messages found in @soul_scanner_bot.")
                        break  # Process only the first token found
                    else:
                        print("⚠️ No token found in the URL.")

# Step 21: Start the client
async def main():
    await client.start(phone_number)
    print("🚀 Bot is running...")

    # Step 22: Keep the bot running
    await client.run_until_disconnected()

# Step 23: Run the bot
if __name__ == "__main__":
    with client:
        client.loop.run_until_complete(main())