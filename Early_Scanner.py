from telethon import Telegram<PERSON>lient, events
import asyncio
import re
import logging
import os
import time  # ULTRA-FAST: Added for performance timing
from datetime import datetime
import pytz
from openpyxl import Workbook, load_workbook
from openpyxl.styles import PatternFill
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Early Scanner specific credentials
API_ID = os.getenv("EARLY_API_ID")
API_HASH = os.getenv("EARLY_API_HASH")
PHONE = os.getenv("EARLY_PHONE")
DESTINATION_BOT = os.getenv("SOUL_SCANNER_BOT", "@soul_scanner_bot")

# Early-specific configuration
SOURCE_USERNAME = "@solearlytrending"
SESSION_FILE = "Early/Early_Scanner.session"  # Session inside folder
EXCEL_FILE = "Early_Scanner_data.xlsx"        # Excel in main directory
BLACKLIST = ["SellAmount"]

# Token patterns for early trending
TOKEN_PATTERN_TYPE1 = r"https://t\.me/soul_sniper_bot\?start=15_([A-Za-z0-9]{43,44})"
TOKEN_PATTERN_TYPE2 = r"https://www\.geckoterminal\.com/solana/pools/([A-Za-z0-9]{43,44})"

# Message filtering patterns
TYPE1_PATTERN = ["New Trending"]  # Process Type 1 messages containing these words
TYPE2_PATTERN = ["is up"]  # Process Type 2 messages containing these words

# Script settings
RATE_LIMIT_DELAY = 3
WAIT_TIME = 5

# ULTRA-FAST Global variables
client = None
processed_tokens = set()
# REMOVED processing_lock for MAXIMUM SPEED
message_queue = asyncio.Queue(maxsize=1000)  # GENIUS: High-speed queue
WORKER_COUNT = 5  # ULTRA-FAST: Multiple parallel workers
stats = {"processed": 0, "queued": 0, "responses": 0}

HEADERS = [
    # Your specified order first
    "Timestamp", "Token Address", "Token Name", "Profit", "Warnings", "Age", "MC", "T-MC",
    "Liq", "Liq SOL", "First 20 Percentage", "First 20", "Whale Fish Pattern",
    "Made", "Volume", "Price", "Scans", "Hodls", "Top Holders", "Snipers",
    "Snipers Percentage",
    # Remaining columns in any order
    "Dex Paid", "High", "Dev", "LP Burnt", "Bundled", "Airdrop", "Burnt",
    "Sold", "Bond"
]

# Profit color fills
GREEN_FILL = PatternFill(start_color="00FF00", end_color="00FF00", fill_type="solid")  # 10X-19.9X
BLUE_FILL = PatternFill(start_color="0000FF", end_color="0000FF", fill_type="solid")   # >20X
ORANGE_FILL = PatternFill(start_color="FFA500", end_color="FFA500", fill_type="solid") # 7X-7.9X
RED_FILL = PatternFill(start_color="FF0000", end_color="FF0000", fill_type="solid")    # 5X-5.9X
PURPLE_FILL = PatternFill(start_color="800080", end_color="800080", fill_type="solid") # 3X-3.9X
YELLOW_FILL = PatternFill(start_color="FFFF00", end_color="FFFF00", fill_type="solid") # 2X-2.9X
GREY_FILL = PatternFill(start_color="808080", end_color="808080", fill_type="solid")   # 50%-99%

# Morocco timezone
MOROCCO_TZ = pytz.timezone('Africa/Casablanca')

# Logging configuration (console only)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def initialize_excel():
    """Initialize Excel file with headers if it doesn't exist"""
    if not os.path.exists(EXCEL_FILE):
        wb = Workbook()
        ws = wb.active
        ws.append(HEADERS)
        wb.save(EXCEL_FILE)
        logger.info(f"✅ Created new Excel file: {EXCEL_FILE}")
    else:
        logger.info(f"📊 Using existing Excel file: {EXCEL_FILE}")

def extract_token_address_from_message(message):
    """Extract token address from message text, entities, and buttons."""
    token_address = None

    # First, try to extract from message text (existing patterns)
    message_text = message.message or ""

    # Type 1 pattern in text
    token_match = re.search(TOKEN_PATTERN_TYPE1, message_text)
    if token_match:
        token_address = token_match.group(1)
        logger.info(f"🎯 Found token in message text (Type 1): {token_address}")
        return token_address

    # Type 2 pattern in text
    token_match = re.search(TOKEN_PATTERN_TYPE2, message_text)
    if token_match:
        token_address = token_match.group(1)
        logger.info(f"🎯 Found token in message text (Type 2): {token_address}")
        return token_address

    # Direct token extraction from text (like FDV1_Scanner)
    # Look for tokens ending with "pump" directly in the message text
    direct_token_pattern = r"([A-Za-z0-9]{39,44}pump)"
    direct_token_match = re.search(direct_token_pattern, message_text)
    if direct_token_match:
        token_address = direct_token_match.group(1)
        logger.info(f"🎯 Found token directly in message text: {token_address}")
        return token_address

    # Extract from message entities (URLs)
    if message.entities:
        for entity in message.entities:
            if hasattr(entity, 'url') and entity.url:
                url = entity.url

                # Check Type 1 pattern in entity URL
                token_match = re.search(TOKEN_PATTERN_TYPE1, url)
                if token_match:
                    token_address = token_match.group(1)
                    logger.info(f"🎯 Found token in entity URL (Type 1): {token_address}")
                    return token_address

                # Check Type 2 pattern in entity URL
                token_match = re.search(TOKEN_PATTERN_TYPE2, url)
                if token_match:
                    token_address = token_match.group(1)
                    logger.info(f"🎯 Found token in entity URL (Type 2): {token_address}")
                    return token_address

    # Extract from message buttons
    if message.buttons:
        for button_row in message.buttons:
            for button in button_row:
                if hasattr(button, 'url') and button.url:
                    url = button.url

                    # Check Type 1 pattern in button URL
                    token_match = re.search(TOKEN_PATTERN_TYPE1, url)
                    if token_match:
                        token_address = token_match.group(1)
                        logger.info(f"🎯 Found token in button URL (Type 1): {token_address}")
                        return token_address

                    # Check Type 2 pattern in button URL
                    token_match = re.search(TOKEN_PATTERN_TYPE2, url)
                    if token_match:
                        token_address = token_match.group(1)
                        logger.info(f"🎯 Found token in button URL (Type 2): {token_address}")
                        return token_address

    # Extract from reply markup (alternative way to access buttons)
    if hasattr(message, 'reply_markup') and message.reply_markup:
        if hasattr(message.reply_markup, 'rows'):
            for row in message.reply_markup.rows:
                if hasattr(row, 'buttons'):
                    for button in row.buttons:
                        if hasattr(button, 'url') and button.url:
                            url = button.url

                            # Check Type 1 pattern in reply markup URL
                            token_match = re.search(TOKEN_PATTERN_TYPE1, url)
                            if token_match:
                                token_address = token_match.group(1)
                                logger.info(f"🎯 Found token in reply markup URL (Type 1): {token_address}")
                                return token_address

                            # Check Type 2 pattern in reply markup URL
                            token_match = re.search(TOKEN_PATTERN_TYPE2, url)
                            if token_match:
                                token_address = token_match.group(1)
                                logger.info(f"🎯 Found token in reply markup URL (Type 2): {token_address}")
                                return token_address

    logger.info("⚠️ No token address found in message, entities, or buttons")
    return None

def extract_profit_from_message(message_text):
    """Extract profit value from Type 2 message with enhanced pattern matching."""
    if not message_text:
        return None

    # 1. Match standard format: "is up 53%" or "is up 5.3X" with flexible spacing/formatting
    profit_match = re.search(r"is\s+up\s*[^\d]*(\d+\.?\d*)([%Xx])", message_text, re.IGNORECASE)
    if profit_match:
        value = profit_match.group(1)
        unit = profit_match.group(2).upper()  # Normalize unit to uppercase
        return f"{value}{unit}"

    # 2. Try arrow notation format: "$50.6K —> $77.1K 💵" with flexible arrow styles
    mc_match = re.search(r"\$(\d+\.?\d*)([KMB])?\s*(?:—>|->|=>|⇒)\s*\$(\d+\.?\d*)([KMB])?", message_text)
    if mc_match:
        # Extract values and convert to float
        start_value = float(mc_match.group(1))
        start_unit = mc_match.group(2) or ''
        end_value = float(mc_match.group(3))
        end_unit = mc_match.group(4) or ''

        # Convert to same units
        multipliers = {'': 1, 'K': 1000, 'M': 1000000, 'B': 1000000000}
        start_amount = start_value * multipliers[start_unit]
        end_amount = end_value * multipliers[end_unit]

        # Calculate percentage or X increase
        if end_amount > start_amount:
            increase_ratio = end_amount / start_amount
            if increase_ratio >= 2:
                # X format for 2X or more
                return f"{increase_ratio:.1f}X"
            else:
                # Percentage format for less than 2X
                percentage = (increase_ratio - 1) * 100
                return f"{percentage:.0f}%"

    # 3. Look for a specific pattern with the words "up" and percentage/X value near each other
    up_val_match = re.search(r"up\s+(?:by\s+)?(\d+\.?\d*)([%Xx])", message_text, re.IGNORECASE)
    if up_val_match:
        value = up_val_match.group(1)
        unit = up_val_match.group(2).upper()
        return f"{value}{unit}"

    # 4. Look for standalone values that might represent profits
    # This is the most aggressive matching and only use if nothing else works
    profit_indicators = ["profit", "gain", "up", "increased", "jumped", "gained"]
    if any(indicator in message_text.lower() for indicator in profit_indicators):
        # Look for percentage or X values
        standalone_match = re.search(r"(\d+\.?\d*)([%Xx])", message_text)
        if standalone_match:
            value = standalone_match.group(1)
            unit = standalone_match.group(2).upper()
            return f"{value}{unit}"

    return None

def extract_data_from_scanner_response(message_text, token_address):
    """Extract data from scanner bot response"""
    data = {
        "Token Address": token_address,
        "Token Name": "N/A",
        "Profit": "Lost",
        "Warnings": "N/A",
        "Age": "N/A",
        "MC": "N/A",
        "T-MC": "N/A",
        "Liq": "N/A",
        "Liq SOL": "N/A",
        "Volume": "N/A",
        "Price": "N/A",
        "Dex Paid": "N/A",
        "Scans": "N/A",
        "Hodls": "N/A",
        "High": "N/A",
        "Snipers": "N/A",
        "Snipers Percentage": "N/A",
        "Dev": "N/A",
        "Top Holders": "N/A",
        "LP Burnt": "N/A",
        "Bundled": "N/A",
        "Airdrop": "N/A",
        "Burnt": "N/A",
        "Sold": "N/A",
        "Made": "N/A",
        "Bond": "N/A",
        "First 20 Percentage": "N/A",
        "First 20": "N/A",
        "Whale Fish Pattern": "N/A"
    }

    # Token Name
    token_name_match = re.search(r"\*\*\$([A-Za-z0-9]+)\*\*", message_text)
    if token_name_match:
        data["Token Name"] = token_name_match.group(1)

    # Warnings extraction
    warnings = re.findall(r"(🚨|⚠️)?\s*__([^__]+)__", message_text)
    if warnings:
        filtered_warnings = [
            f"{icon} {warning}" if icon else warning
            for icon, warning in warnings
            if any(keyword.lower() in warning.lower()
                 for keyword in ["High", "Migrating", "Low", "Dev", "Sniped", "Abnormal"])
        ]
        if filtered_warnings:
            data["Warnings"] = " | ".join(filtered_warnings)

    # Age
    age_match = re.search(r"🕒 \*\*Age:\*\* ([^\n]+)", message_text)
    if age_match:
        data["Age"] = age_match.group(1).strip()

    # MC (Market Cap)
    mc_match = re.search(r"💰 \*\*MC:\*\* ([^\s•]+)", message_text)
    if mc_match:
        data["MC"] = mc_match.group(1).strip()

    # T-MC (Target Market Cap)
    t_mc_match = re.search(r"🔝 __([^_]+)__", message_text)
    if t_mc_match:
        data["T-MC"] = t_mc_match.group(1).strip()

    # Liquidity
    liq_match = re.search(r"💧 \*\*Liq:\*\* ([^\s•]+)", message_text)
    if liq_match:
        data["Liq"] = liq_match.group(1).strip()

    # Liquidity SOL
    liq_sol_match = re.search(r"💧.*?(\d+\.?\d* SOL)", message_text)
    if liq_sol_match:
        data["Liq SOL"] = liq_sol_match.group(1)

    # Volume - Updated pattern for: 📈 **Vol:** __1h__: $527.9K | __1d__: $1.4M
    volume_match = re.search(r"📈 \*\*Vol:\*\* ([^📈\n]+)", message_text)
    if volume_match:
        data["Volume"] = volume_match.group(1).strip()

    # Price
    price_match = re.search(r"📈 \*\*Price:\*\* ([^*\n]+)", message_text)
    if price_match:
        data["Price"] = price_match.group(1).strip()

    # Dex Paid
    if "✅" in message_text and "Dex" in message_text:
        data["Dex Paid"] = "✅"
    elif "❌" in message_text and "Dex" in message_text:
        data["Dex Paid"] = "❌"

    # Scans - Updated pattern for: ⚡ **Scans:** 627 | 🔗 [X](https://x.com/...)
    scans_match = re.search(r"⚡ \*\*Scans:\*\* (\d+)", message_text)
    if scans_match:
        data["Scans"] = scans_match.group(1)

    # Hodls - Updated pattern for: **👥 **[**Hodls**](...): 1,438 • Top: 15.5%
    hodls_match = re.search(r"\*\*👥 \*\*\[\*\*Hodls\*\*\]\([^)]+\): ([\d,]+)", message_text)
    if hodls_match:
        data["Hodls"] = hodls_match.group(1)

    # High - Multiple patterns for: ┗ High: [5.2%](link) or ┗ High: 5.2% or High: 5.2%
    high_match = re.search(r"┗ High: \[(\d+\.?\d*%)\]", message_text)  # [5.2%](link) format
    if not high_match:
        high_match = re.search(r"┗ High: (\d+\.?\d*%)", message_text)  # 5.2% format
    if not high_match:
        # Alternative pattern for other High formats
        high_match = re.search(r"High: (\d+\.?\d*%)", message_text)

    if high_match:
        data["High"] = high_match.group(1)

    # Snipers
    snipers_match = re.search(r"🎯 \*\*Snipers:\*\* (\d+)", message_text)
    if snipers_match:
        data["Snipers"] = snipers_match.group(1)

    # Snipers Percentage
    snipers_percentage_match = re.search(r"🎯.*?(\d+\.?\d*%)", message_text)
    if snipers_percentage_match:
        data["Snipers Percentage"] = snipers_percentage_match.group(1)

    # Dev
    dev_match = re.search(r"🛠️ Dev.*?(\d+ SOL)", message_text)
    if dev_match:
        data["Dev"] = dev_match.group(1)

    # Top Holders - Updated pattern for: **👥 **[**Hodls**](...): 1,438 • Top: 15.5%
    top_holders_match = re.search(r"\*\*👥 \*\*\[\*\*Hodls\*\*\]\([^)]+\): [\d,]+ • Top: (\d+\.?\d*%)", message_text)
    if top_holders_match:
        data["Top Holders"] = top_holders_match.group(1)

    # LP Burnt
    lp_match = re.search(r"\*\*:\*\* (\d+%) Burnt", message_text)
    if lp_match:
        data["LP Burnt"] = lp_match.group(1)

    # Sold
    sold_match = re.search(r"\| Sold:\s*(\d+\.?\d*%)", message_text)
    if sold_match:
        data["Sold"] = sold_match.group(1)

    # Made patterns
    made_match = re.search(r"Made: (\d+)", message_text)
    if made_match:
        data["Made"] = made_match.group(1)

    # Bond patterns
    bond_match = re.search(r"Bond: (\d+)", message_text)
    if bond_match:
        data["Bond"] = bond_match.group(1)

    # Bundled patterns
    bundled_match = re.search(r"Bundled: (\d+%)", message_text)
    if bundled_match:
        data["Bundled"] = bundled_match.group(1)

    # Airdrop patterns
    airdrop_match = re.search(r"Airdrop: (\d+%)", message_text)
    if airdrop_match:
        data["Airdrop"] = airdrop_match.group(1)

    # Burnt patterns (different from LP Burnt)
    burnt_match = re.search(r"Burnt: ([^🔥\n]+)", message_text)
    if burnt_match:
        data["Burnt"] = burnt_match.group(1).strip()

    # First 20 detailed patterns - extract from the summary line
    # Pattern: [🎯 First 20](link): 26% | 11 🐟 • 19%
    first20_detailed = re.search(r"\*\*:\*\* (\d+%) \| (\d+) ([🛠🐟🍤🐳🌱]) • (\d+%)", message_text)
    if first20_detailed:
        data["First 20 Percentage"] = first20_detailed.group(1)
        count = first20_detailed.group(2)
        emoji = first20_detailed.group(3)
        percentage = first20_detailed.group(4)
        data["First 20"] = f"{count} {emoji} • {percentage}"

    # Extract whale/fish patterns and analyze them
    whale_fish_patterns = extract_whale_fish_patterns(message_text)
    if whale_fish_patterns:
        data.update(whale_fish_patterns)

    return data

def extract_whale_fish_patterns(text):
    """Extract whale/fish emoji patterns"""
    patterns = {}
    
    # Extract the detailed holder pattern from the lines with solscan links
    # Look for pattern like: [🛠](https://solscan.io/account/...)[🐟](https://solscan.io/account/...)
    holder_pattern_match = re.findall(r'\[([🛠🐟🍤🐳🌱])\]\(https://solscan\.io/account/[^)]+\)', text)
    
    if holder_pattern_match:
        # Combine all holder emojis to create the full pattern
        all_patterns = ''.join(holder_pattern_match)
        patterns['Whale Fish Pattern'] = all_patterns
    
    return patterns

def parse_market_cap_value(mc_text):
    """Parse market cap text and return numeric value in thousands"""
    if not mc_text or mc_text == "N/A":
        return 0

    # Remove $ and convert to uppercase
    mc_clean = mc_text.replace("$", "").replace(",", "").upper()

    try:
        if "K" in mc_clean:
            # Convert K to thousands
            value = float(mc_clean.replace("K", ""))
            return value
        elif "M" in mc_clean:
            # Convert M to thousands (1M = 1000K)
            value = float(mc_clean.replace("M", ""))
            return value * 1000
        elif "B" in mc_clean:
            # Convert B to thousands (1B = 1,000,000K)
            value = float(mc_clean.replace("B", ""))
            return value * 1000000
        else:
            # Assume it's already in dollars, convert to thousands
            value = float(mc_clean)
            return value / 1000
    except (ValueError, AttributeError):
        return 0

def get_tmc_color(tmc_value_k):
    """Get color fill for T-MC based on value in thousands"""
    if 30 <= tmc_value_k <= 60:
        # Yellow
        return PatternFill(start_color="FFFF00", end_color="FFFF00", fill_type="solid")
    elif 61 <= tmc_value_k <= 100:
        # Light Green
        return PatternFill(start_color="90EE90", end_color="90EE90", fill_type="solid")
    elif tmc_value_k >= 101:
        # Light Blue
        return PatternFill(start_color="ADD8E6", end_color="ADD8E6", fill_type="solid")
    else:
        # No color for values below $30K
        return None

async def export_to_excel(data):
    """🚀 ULTRA-FAST Excel export - NO LOCKS for maximum speed."""
    try:
        wb = load_workbook(EXCEL_FILE)
        ws = wb.active

        # Check if token already exists
        token_exists = False
        existing_row_idx = None
        for row_idx, row in enumerate(ws.iter_rows(min_row=2, values_only=True), start=2):
            if row and len(row) > 1 and row[1] == data.get("Token Address"):  # Token Address is in column 2
                token_exists = True
                existing_row_idx = row_idx
                break

        # Handle profit-only updates for existing tokens (TYPE2 messages)
        if token_exists and len(data.keys()) <= 3 and "Profit" in data and data.get("Profit") != "N/A":
            # This is just a profit update for an existing token
            profit_col_idx = 4  # Profit is in column 4
            ws.cell(row=existing_row_idx, column=profit_col_idx, value=data["Profit"])
            cell = ws.cell(row=existing_row_idx, column=profit_col_idx)

            # Update X URL if provided
            if "X URL" in data and data["X URL"] != "N/A":
                # Find X URL column (assuming it's in the headers)
                headers = [cell.value for cell in ws[1]]
                if "X URL" in headers:
                    x_url_col_idx = headers.index("X URL") + 1
                    ws.cell(row=existing_row_idx, column=x_url_col_idx, value=data["X URL"])

            # Apply color based on profit value
            profit_value = data["Profit"]
            if profit_value and profit_value != "N/A" and profit_value != "Lost":
                try:
                    if '%' in profit_value:
                        profit_num = float(profit_value.replace('%', '').strip())
                        if 50 <= profit_num < 100:
                            cell.fill = GREY_FILL
                    elif 'X' in profit_value:
                        profit_num = float(profit_value.replace('X', '').strip())
                        if 2 <= profit_num < 3:
                            cell.fill = YELLOW_FILL
                        elif 3 <= profit_num < 4:
                            cell.fill = PURPLE_FILL
                        elif 5 <= profit_num < 6:
                            cell.fill = RED_FILL
                        elif 7 <= profit_num < 8:
                            cell.fill = ORANGE_FILL
                        elif 10 <= profit_num < 20:
                            cell.fill = GREEN_FILL
                        elif profit_num >= 20:
                            cell.fill = BLUE_FILL
                except ValueError:
                    logger.warning(f"Could not parse profit value: {profit_value}")

            logger.info(f"✅ TYPE2: Updated profit {data['Profit']} for existing token {data['Token Address']}")
            wb.save(EXCEL_FILE)
            logger.info(f"✅ Data saved to {EXCEL_FILE}")
            return

        # Handle TYPE2 profit updates for non-existing tokens (skip them)
        elif not token_exists and len(data.keys()) <= 3 and "Profit" in data:
            logger.info(f"⚠️ TYPE2: Token {data['Token Address']} not found in sheet, skipping profit update")
            return

        # Handle full token data (new tokens or complete updates)
        elif not token_exists:
            # For profit-only updates on non-existing tokens, create a minimal entry
            if len(data.keys()) <= 3 and "Profit" in data:
                    # Create minimal token entry with just the profit data
                    morocco_time = datetime.now(MOROCCO_TZ).strftime("%Y-%m-%d %H:%M:%S")
                    row = [
                        morocco_time,
                        data["Token Address"],
                        "N/A",  # Token Name
                        data["Profit"],
                        "N/A",  # Warnings
                        "N/A",  # Age
                        "N/A",  # MC
                        "N/A",  # T-MC
                        "N/A",  # Liq
                        "N/A",  # Liq SOL
                        "N/A",  # First 20 Percentage
                        "N/A",  # First 20
                        "N/A",  # Whale Fish Pattern
                        "N/A",  # Made
                        "N/A",  # Volume
                        "N/A",  # Price
                        "N/A",  # Scans
                        "N/A",  # Hodls
                        "N/A",  # Top Holders
                        "N/A",  # Snipers
                        "N/A",  # Snipers Percentage
                        "N/A",  # Dex Paid
                        "N/A",  # High
                        "N/A",  # Dev
                        "N/A",  # LP Burnt
                        "N/A",  # Bundled
                        "N/A",  # Airdrop
                        "N/A",  # Burnt
                        "N/A",  # Sold
                        "N/A"   # Bond
                    ]
            else:
                # Full token data
                morocco_time = datetime.now(MOROCCO_TZ).strftime("%Y-%m-%d %H:%M:%S")
                row = [
                    # Your specified order first
                    morocco_time,
                    data.get("Token Address", "N/A"),
                    data.get("Token Name", "N/A"),
                    data.get("Profit", "N/A"),
                    data.get("Warnings", "N/A"),
                    data.get("Age", "N/A"),
                    data.get("MC", "N/A"),
                    data.get("T-MC", "N/A"),
                    data.get("Liq", "N/A"),
                    data.get("Liq SOL", "N/A"),
                    data.get("First 20 Percentage", "N/A"),
                    data.get("First 20", "N/A"),
                    data.get("Whale Fish Pattern", "N/A"),
                    data.get("Made", "N/A"),
                    data.get("Volume", "N/A"),
                    data.get("Price", "N/A"),
                    data.get("Scans", "N/A"),
                    data.get("Hodls", "N/A"),
                    data.get("Top Holders", "N/A"),
                    data.get("Snipers", "N/A"),
                    data.get("Snipers Percentage", "N/A"),
                    # Remaining columns in any order
                    data.get("Dex Paid", "N/A"),
                    data.get("High", "N/A"),
                    data.get("Dev", "N/A"),
                    data.get("LP Burnt", "N/A"),
                    data.get("Bundled", "N/A"),
                    data.get("Airdrop", "N/A"),
                    data.get("Burnt", "N/A"),
                    data.get("Sold", "N/A"),
                    data.get("Bond", "N/A")
                ]
            ws.append(row)

            # Apply profit column coloring for new tokens
            current_row = ws.max_row
            profit_column_index = 4  # Profit is the 4th column
            profit_cell = ws.cell(row=current_row, column=profit_column_index)

            # Apply color based on profit value
            profit_value = data.get("Profit", "N/A")
            if profit_value and profit_value != "N/A" and profit_value != "Lost":
                try:
                    if '%' in profit_value:
                        profit_num = float(profit_value.replace('%', '').strip())
                        if 50 <= profit_num < 100:
                            profit_cell.fill = GREY_FILL
                    elif 'X' in profit_value:
                        profit_num = float(profit_value.replace('X', '').strip())
                        if 2 <= profit_num < 3:
                            profit_cell.fill = YELLOW_FILL
                        elif 3 <= profit_num < 4:
                            profit_cell.fill = PURPLE_FILL
                        elif 5 <= profit_num < 6:
                            profit_cell.fill = RED_FILL
                        elif 7 <= profit_num < 8:
                            profit_cell.fill = ORANGE_FILL
                        elif 10 <= profit_num < 20:
                            profit_cell.fill = GREEN_FILL
                        elif profit_num >= 20:
                            profit_cell.fill = BLUE_FILL
                except ValueError:
                    logger.warning(f"Could not parse profit value: {profit_value}")

            # Apply T-MC column coloring for new tokens (only if T-MC data exists)
            if "T-MC" in data and data["T-MC"] != "N/A":
                tmc_column_index = 8  # T-MC is the 8th column (1-based)
                tmc_cell = ws.cell(row=current_row, column=tmc_column_index)

                # Parse T-MC value and apply color
                tmc_value_k = parse_market_cap_value(data["T-MC"])
                tmc_color = get_tmc_color(tmc_value_k)

                if tmc_color:
                    tmc_cell.fill = tmc_color
                    logger.info(f"🎨 Applied color to T-MC: {data['T-MC']} (${tmc_value_k}K)")

            logger.info(f"✅ Added new row for token {data['Token Address']}")

        wb.save(EXCEL_FILE)
        logger.info(f"✅ Data saved to {EXCEL_FILE}")
    except Exception as e:
        logger.error(f"❌ Failed to export data to Excel: {e}")

async def forward_token(token_address):
    """Forward the token address to the destination."""
    try:
        await client.send_message(DESTINATION_BOT, token_address)
        logger.info(f"✅ Forwarded token to destination: {token_address}")
    except Exception as e:
        logger.error(f"❌ Failed to forward token: {e}")

async def message_handler(event):
    """🚀 ULTRA-FAST message handler - ZERO DELAYS, INSTANT PROCESSING"""
    # NO PROCESSING LOCK - Allow parallel processing for MAXIMUM SPEED
    # NO DELAYS - Process immediately

    message = event.message
    message_text = message.message

    # INSTANT skip check
    if not message_text:
        return

    # INSTANT pattern check
    message_text_lower = message_text.lower()
    has_type1_pattern = any(pattern.lower() in message_text_lower for pattern in TYPE1_PATTERN)
    has_type2_pattern = any(pattern.lower() in message_text_lower for pattern in TYPE2_PATTERN)

    # INSTANT filtering - only process relevant messages
    if not (has_type1_pattern or has_type2_pattern):
        return

    # INSTANT queuing - NO DELAYS
    try:
        await message_queue.put((time.time(), message, has_type1_pattern, has_type2_pattern))
        stats["queued"] += 1
        logger.info(f"⚡ ULTRA-FAST QUEUED: {message_text[:40]}... | Queue: {message_queue.qsize()}")
    except asyncio.QueueFull:
        logger.warning("🚨 Queue full! Processing at MAXIMUM SPEED!")

# 🚀 ULTRA-FAST WORKER FUNCTIONS
async def ultra_fast_worker(worker_id):
    """🚀 GENIUS: Ultra-fast parallel worker for MAXIMUM SPEED"""
    logger.info(f"🚀 ULTRA-FAST-WORKER-{worker_id}: Started")

    while True:
        try:
            # Get message from queue INSTANTLY
            timestamp, message, has_type1_pattern, has_type2_pattern = await message_queue.get()
            queue_time = time.time() - timestamp

            logger.info(f"⚡ WORKER-{worker_id}: Processing (queued {queue_time:.2f}s)")

            # ULTRA-FAST processing
            await process_message_ultra_fast(message, has_type1_pattern, has_type2_pattern, worker_id)

            # Mark task done
            message_queue.task_done()
            stats["processed"] += 1

        except Exception as e:
            logger.error(f"❌ WORKER-{worker_id}: {e}")
            await asyncio.sleep(0.01)  # Minimal recovery delay

async def process_message_ultra_fast(message, has_type1_pattern, has_type2_pattern, worker_id):
    """🚀 ULTRA-FAST message processing with ZERO unnecessary delays"""
    message_text = message.message
    message_text_lower = message_text.lower()

    if has_type1_pattern:
        logger.info(f"🔥 WORKER-{worker_id}: Type 1 (New Trending)")

        # INSTANT token extraction
        token_address = extract_token_address_from_message(message)
        if not token_address:
            return

        # SPEED CHECK: Skip duplicates instantly
        if token_address in processed_tokens:
            return

        # INSTANT blacklist check
        if BLACKLIST and any(word.lower() in message_text_lower for word in BLACKLIST):
            return

        # Mark processed IMMEDIATELY
        processed_tokens.add(token_address)

        logger.info(f"🎯 WORKER-{worker_id}: NEW TOKEN: {token_address}")

        # INSTANT bot communication
        try:
            await client.send_message(DESTINATION_BOT, token_address)
            logger.info(f"⚡ WORKER-{worker_id}: Sent to bot INSTANTLY")

            # GENIUS: Start ultra-fast response monitoring
            response_data = await ultra_fast_response_monitor(token_address, worker_id)

            # INSTANT Excel save
            await export_to_excel(response_data)
            logger.info(f"✅ WORKER-{worker_id}: Excel updated INSTANTLY")

        except Exception as e:
            logger.error(f"❌ WORKER-{worker_id}: Error: {e}")

    elif has_type2_pattern:
        logger.info(f"📈 WORKER-{worker_id}: Type 2 (Profit Update) - NO BOT SCANNING")

        # INSTANT token and profit extraction
        token_address = extract_token_address_from_message(message)
        if not token_address:
            logger.info(f"⚠️ WORKER-{worker_id}: No token found in Type 2 message")
            return

        # Extract profit value from the message
        profit_value = extract_profit_from_message(message_text)
        if not profit_value:
            logger.info(f"⚠️ WORKER-{worker_id}: No profit value found in Type 2 message")
            return

        # Extract X URL if present
        x_url = "N/A"
        if hasattr(message, 'entities') and message.entities:
            for entity in message.entities:
                if hasattr(entity, 'url') and ('x.com' in entity.url or 'twitter.com' in entity.url):
                    x_url = entity.url
                    logger.info(f"🐦 WORKER-{worker_id}: Found X URL: {x_url}")
                    break

        # Create profit update data (TYPE2 - only profit update, no bot scanning)
        profit_data = {
            "Token Address": token_address,
            "Profit": profit_value,
            "X URL": x_url
        }

        # Update Excel directly (this will only update existing tokens)
        await export_to_excel(profit_data)
        logger.info(f"✅ WORKER-{worker_id}: Profit updated INSTANTLY for {token_address} -> {profit_value}")

async def ultra_fast_response_monitor(token_address, worker_id):
    """🚀 HYBRID: INSTANT detection + 5s timeout for complete bot response"""
    # 🎯 HYBRID: Wait 5 seconds for bot to process completely
    await asyncio.sleep(5)  # Give bot proper time to process and respond with complete data
    logger.info(f"⏳ WORKER-{worker_id}: Waited 5s, now checking for complete response...")

    # Enhanced response detection with multiple attempts
    for attempt in range(3):  # 3 attempts over 6 additional seconds
        try:
            # Check for complete response
            messages = await client.get_messages(DESTINATION_BOT, limit=20)

            for msg in messages:
                if msg.text and (
                    token_address in msg.text or
                    (len(token_address) > 10 and token_address[:10] in msg.text) or
                    any(indicator in msg.text for indicator in ["**$", "MC:", "Age:", "Scans:"])
                ):
                    total_time = 5 + (attempt + 1) * 2
                    stats["responses"] += 1
                    logger.info(f"🔍 WORKER-{worker_id}: Found complete response in {total_time}s!")

                    # Extract complete data
                    return extract_data_from_scanner_response(msg.text, token_address)

        except Exception as e:
            logger.error(f"❌ WORKER-{worker_id}: Monitor error: {e}")

        # Wait 2 more seconds before next attempt (except on last attempt)
        if attempt < 2:
            logger.info(f"⏳ WORKER-{worker_id}: No response yet, waiting 2 more seconds... (attempt {attempt + 1}/3)")
            await asyncio.sleep(2)

    # Return minimal data if no response after 5s + 6s = 11s total
    logger.warning(f"⚠️ WORKER-{worker_id}: No response after 11s, saving minimal data")
    return {
        "Token Address": token_address,
        "Token Name": "N/A",
        "Profit": "N/A"
    }

# 🚀 ULTRA-FAST PROCESSING COMPLETE - All processing now handled by parallel workers

async def print_ultra_fast_stats():
    """Print ULTRA-FAST performance statistics"""
    while True:
        await asyncio.sleep(30)  # Every 30 seconds
        logger.info(f"📊 ULTRA-FAST STATS: Processed:{stats['processed']} | Queued:{stats['queued']} | Responses:{stats['responses']} | Queue:{message_queue.qsize()}")

async def main():
    """🚀 ULTRA-FAST Main function with parallel workers"""
    global client

    logger.info("🚀 Starting HYBRID ULTRA-FAST Early Scanner")
    logger.info(f"📍 Source: {SOURCE_USERNAME}")
    logger.info(f"📍 Destination: {DESTINATION_BOT}")
    logger.info(f"📍 Workers: {WORKER_COUNT}")
    logger.info(f"📊 Excel File: {EXCEL_FILE}")
    logger.info("⚡ HYBRID MODE: INSTANT detection + 5s bot timeout")
    logger.info("🎯 TYPE1 (New Trending): Full bot scan + new Excel row")
    logger.info("📈 TYPE2 (is up): Profit extraction + update existing row only")
    logger.info("=" * 75)

    # Initialize Excel file
    initialize_excel()

    # Initialize Telegram client
    client = TelegramClient(SESSION_FILE.replace('.session', ''), API_ID, API_HASH)

    try:
        # Start the client
        await client.start(phone=PHONE)
        logger.info("✅ Client started ULTRA-FAST")

        # Resolve the source channel entity
        try:
            source_entity = await client.get_entity(SOURCE_USERNAME)
            logger.info(f"✅ Resolved source channel: {source_entity.title} (ID: {source_entity.id})")
        except Exception as e:
            logger.error(f"❌ Failed to resolve source channel {SOURCE_USERNAME}: {e}")
            return

        # 🚀 START ULTRA-FAST WORKERS
        workers = []
        for i in range(WORKER_COUNT):
            worker = asyncio.create_task(ultra_fast_worker(i + 1))
            workers.append(worker)

        # Start stats printer
        asyncio.create_task(print_ultra_fast_stats())

        logger.info(f"🚀 Started {WORKER_COUNT} ULTRA-FAST workers")

        # Register the HYBRID ULTRA-FAST event handler
        client.add_event_handler(message_handler, events.NewMessage(chats=[source_entity]))
        logger.info("⚡ HYBRID LISTENING: INSTANT detection + 5s bot timeout for complete data!")

        # Keep the client running to listen for events
        await client.run_until_disconnected()

    except Exception as e:
        logger.error(f"❌ Error in main: {e}")
    finally:
        if client:
            await client.disconnect()
            logger.info("🔌 Client disconnected")

if __name__ == "__main__":
    asyncio.run(main())
