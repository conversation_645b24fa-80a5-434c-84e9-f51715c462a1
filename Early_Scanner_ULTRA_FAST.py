from telethon import Telegram<PERSON>lient, events
import asyncio
import re
import logging
import os
import time
from datetime import datetime
import pytz
from openpyxl import Workbook, load_workbook
from openpyxl.styles import PatternFill
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Early Scanner specific credentials
API_ID = os.getenv("EARLY_API_ID")
API_HASH = os.getenv("EARLY_API_HASH")
PHONE = os.getenv("EARLY_PHONE")
DESTINATION_BOT = os.getenv("SOUL_SCANNER_BOT", "@soul_scanner_bot")

# Early-specific configuration
SOURCE_USERNAME = "@solearlytrending"
SESSION_FILE = "Early/Early_Scanner_ULTRA_FAST.session"
EXCEL_FILE = "Early_Scanner_ULTRA_FAST_data.xlsx"
WHITELIST = ["New Trending"]
BLACKLIST = ["SellAmount"]

# ULTRA-FAST SETTINGS - ZERO DELAYS
WORKER_COUNT = 5  # Multiple parallel workers
MAX_QUEUE_SIZE = 1000  # Large queue for burst handling

# Global variables for ULTRA-FAST processing
client = None
processed_tokens = set()
message_queue = asyncio.Queue(maxsize=MAX_QUEUE_SIZE)
response_cache = {}  # Cache responses for speed

# Morocco timezone
MOROCCO_TZ = pytz.timezone('Africa/Casablanca')

# ULTRA-FAST logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s', handlers=[logging.StreamHandler()])
logger = logging.getLogger(__name__)

# Excel headers
HEADERS = ["Timestamp", "Token Address", "Token Name", "Profit"]

def initialize_excel():
    if not os.path.exists(EXCEL_FILE):
        wb = Workbook()
        ws = wb.active
        ws.append(HEADERS)
        wb.save(EXCEL_FILE)
        logger.info(f"✅ Created ULTRA-FAST Excel file: {EXCEL_FILE}")
    else:
        logger.info(f"📊 Using existing ULTRA-FAST Excel file: {EXCEL_FILE}")

def extract_token_address_from_message(message):
    """ULTRA-FAST token extraction with multiple patterns"""
    message_text = message.message or ""
    
    # Pattern 1: Tokens ending with "pump"
    pump_pattern = r"([A-Za-z0-9]{39,40}pump)"
    pump_matches = re.findall(pump_pattern, message_text)
    if pump_matches:
        return pump_matches[0]
    
    # Pattern 2: Soul sniper bot URLs
    sniper_pattern = r"https://t\.me/soul_sniper_bot\?start=15_([A-Za-z0-9]{43,44})"
    sniper_matches = re.findall(sniper_pattern, message_text)
    if sniper_matches:
        return sniper_matches[0]
    
    # Pattern 3: GeckoTerminal URLs
    gecko_pattern = r"https://www\.geckoterminal\.com/solana/pools/([A-Za-z0-9]{43,44})"
    gecko_matches = re.findall(gecko_pattern, message_text)
    if gecko_matches:
        return gecko_matches[0]
    
    return None

def extract_profit_from_message(message_text):
    """ULTRA-FAST profit extraction"""
    profit_patterns = [
        r'(\d+\.?\d*)%',
        r'(\d+\.?\d*)x',
        r'\+(\d+\.?\d*)%'
    ]
    
    for pattern in profit_patterns:
        matches = re.findall(pattern, message_text, re.IGNORECASE)
        if matches:
            return f"{matches[0]}%"
    return None

async def export_to_excel(data):
    """ULTRA-FAST Excel export with minimal locking"""
    try:
        wb = load_workbook(EXCEL_FILE)
        ws = wb.active
        
        # Add timestamp
        timestamp = datetime.now(MOROCCO_TZ).strftime("%Y-%m-%d %H:%M:%S")
        
        ws.append([
            timestamp,
            data.get("Token Address", "N/A"),
            data.get("Token Name", "N/A"),
            data.get("Profit", "N/A")
        ])
        
        wb.save(EXCEL_FILE)
        logger.info(f"⚡ ULTRA-FAST Excel save: {data.get('Token Address', 'N/A')[:10]}...")
        
    except Exception as e:
        logger.error(f"❌ Excel error: {e}")

async def message_handler(event):
    """ULTRA-FAST message handler - ZERO DELAYS, INSTANT QUEUING"""
    message = event.message
    message_text = message.message

    # INSTANT skip check
    if not message_text:
        return

    # INSTANT pattern check
    if not any(pattern.lower() in message_text.lower() for pattern in WHITELIST):
        return

    # INSTANT blacklist check
    if any(word.lower() in message_text.lower() for word in BLACKLIST):
        return

    # INSTANT queuing - NO DELAYS
    try:
        await message_queue.put((time.time(), message))
        logger.info(f"⚡ QUEUED: {message_text[:30]}... | Queue: {message_queue.qsize()}")
    except asyncio.QueueFull:
        logger.warning("🚨 Queue full! Processing at maximum speed!")

async def ultra_fast_worker(worker_id):
    """GENIUS: Ultra-fast parallel worker"""
    logger.info(f"🚀 WORKER-{worker_id}: Started ULTRA-FAST processing")
    
    while True:
        try:
            # Get message instantly
            timestamp, message = await message_queue.get()
            queue_time = time.time() - timestamp
            
            logger.info(f"⚡ WORKER-{worker_id}: Processing (queued {queue_time:.2f}s)")
            
            # ULTRA-FAST processing
            await process_message_ultra_fast(message, worker_id)
            
            # Mark done
            message_queue.task_done()
            
        except Exception as e:
            logger.error(f"❌ WORKER-{worker_id}: {e}")
            await asyncio.sleep(0.01)  # Minimal recovery

async def process_message_ultra_fast(message, worker_id):
    """ULTRA-FAST message processing - ZERO unnecessary operations"""
    message_text = message.message
    
    # INSTANT token extraction
    token_address = extract_token_address_from_message(message)
    if not token_address:
        return

    # SPEED CHECK: Skip duplicates instantly
    if token_address in processed_tokens:
        return

    # Mark processed IMMEDIATELY
    processed_tokens.add(token_address)
    
    logger.info(f"🎯 WORKER-{worker_id}: NEW TOKEN: {token_address}")

    # INSTANT bot communication
    try:
        await client.send_message(DESTINATION_BOT, token_address)
        logger.info(f"⚡ WORKER-{worker_id}: Sent to bot INSTANTLY")
        
        # GENIUS: Start response monitoring immediately
        asyncio.create_task(ultra_fast_response_monitor(token_address, worker_id))
        
    except Exception as e:
        logger.error(f"❌ WORKER-{worker_id}: Send error: {e}")

async def ultra_fast_response_monitor(token_address, worker_id):
    """GENIUS: Ultra-fast response monitoring with 0.3s intervals"""
    for attempt in range(15):  # 15 attempts = 4.5 seconds total
        await asyncio.sleep(0.3)  # Check every 0.3 seconds!
        
        try:
            # Quick message check
            messages = await client.get_messages(DESTINATION_BOT, limit=20)
            
            for msg in messages:
                if msg.text and (
                    token_address in msg.text or
                    (len(token_address) > 8 and token_address[:8] in msg.text) or
                    any(indicator in msg.text for indicator in ["**$", "MC:", "Age:", "Scans:"])
                ):
                    response_time = (attempt + 1) * 0.3
                    logger.info(f"🔍 WORKER-{worker_id}: RESPONSE in {response_time:.1f}s!")
                    
                    # INSTANT data extraction
                    data = {"Token Address": token_address, "Token Name": "N/A", "Profit": "N/A"}
                    
                    # Extract token name quickly
                    name_match = re.search(r"\*\*\$([A-Za-z0-9]+)\*\*", msg.text)
                    if name_match:
                        data["Token Name"] = name_match.group(1)
                    
                    # INSTANT Excel save
                    await export_to_excel(data)
                    return
                    
        except Exception as e:
            logger.error(f"❌ WORKER-{worker_id}: Monitor error: {e}")
    
    # Save minimal data if no response
    await export_to_excel({"Token Address": token_address, "Token Name": "N/A", "Profit": "N/A"})
    logger.info(f"⚠️ WORKER-{worker_id}: Saved minimal data")

async def main():
    """ULTRA-FAST main function with parallel workers"""
    global client
    
    logger.info("🚀 Starting ULTRA-FAST Early Scanner")
    logger.info(f"📍 Source: {SOURCE_USERNAME}")
    logger.info(f"📍 Workers: {WORKER_COUNT}")
    logger.info(f"📊 Excel: {EXCEL_FILE}")
    logger.info("=" * 50)

    initialize_excel()
    client = TelegramClient(SESSION_FILE.replace('.session', ''), API_ID, API_HASH)

    try:
        await client.start(phone=PHONE)
        logger.info("✅ Client started ULTRA-FAST")

        # Resolve source
        source_entity = await client.get_entity(SOURCE_USERNAME)
        logger.info(f"✅ Source resolved: {source_entity.title}")

        # Start MULTIPLE parallel workers
        workers = []
        for i in range(WORKER_COUNT):
            worker = asyncio.create_task(ultra_fast_worker(i + 1))
            workers.append(worker)
        
        logger.info(f"🚀 Started {WORKER_COUNT} ULTRA-FAST workers")

        # Register ULTRA-FAST event handler
        client.add_event_handler(message_handler, events.NewMessage(chats=[source_entity]))
        logger.info("⚡ LISTENING: ULTRA-FAST mode activated!")

        # Keep running
        await client.run_until_disconnected()

    except Exception as e:
        logger.error(f"❌ Error: {e}")
    finally:
        if client:
            await client.disconnect()

if __name__ == "__main__":
    asyncio.run(main())
