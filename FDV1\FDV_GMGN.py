from telethon import Telegram<PERSON>lient, events
import re
import asyncio
import os
from openpyxl import Workbook, load_workbook
from datetime import datetime

# Step 1: Replace these with your own credentials
api_id = "25195947"  # Replace with your API ID
api_hash = "11c84e2a999bbf77b8476c30ca17b12c"  # Replace with your API Hash
phone_number = "+212694457017"  # Replace with your phone number (e.g., "+1234567890")

# Step 2: Source and Destination IDs
source_username = "@gmgnsignals"  # Username of the source bot
destination_username = "@soul_scanner_bot"  # Username of the destination bot

# Step 3: Regex pattern to extract token from text (last 4 characters must be "pump")
token_pattern = r"([A-Za-z0-9]{39,40}pump)"  # Matches a token address ending with "pump"

# Step 4: Regex pattern to extract source age
source_age_pattern = r'Open:\s([^*]+)\sago'  # Matches "Open: **64d** **ago**"

# Step 4: Whitelist and Blacklist
whitelist = ["FDV in 5 min"]  # Process messages containing these words
blacklist = ["SellAmount"]  # Ignore messages containing these words

# Step 5: Rate Limiting (in seconds)
RATE_LIMIT_DELAY = 3  # 3-second delay between token transfers

# Step 6: Waiting time before scraping the destination bot (in seconds)
WAIT_TIME = 5  # Wait for 5 seconds before scraping the destination bot

# Step 7: Initialize the Telegram client
client = TelegramClient("FDV_GMGN", api_id, api_hash)

# Step 8: Excel file setup (added "Source Age" column)
EXCEL_FILE = "FDV_GMGN.xlsx"
if not os.path.exists(EXCEL_FILE):
    wb = Workbook()
    ws = wb.active
    headers = [
        "Timestamp", "Token Address", "Token Name", "Warnings", "Age", "Source Age",
        "MC", "T-MC", "Liq", "Liq SOL", "Vol-1h", "Price-1h", "Scans", "Hodls",
        "High", "Snipers", "Snipers Percentage", "Dev", "Sniped", "Top Holders",
        "Dex", "LP", "First", "First Percentage", "Sold", "Burnt"
    ]
    ws.append(headers)
    wb.save(EXCEL_FILE)

# Step 10: Function to clear the terminal
def clear_terminal():
    """Clear the terminal screen."""
    os.system("cls" if os.name == "nt" else "clear")

# Step 11: Set to track processed tokens
processed_tokens = set()

# Step 12: Variable to track the last processed message ID
last_processed_message_id = None

# Step 13: Lock to ensure sequential processing
processing_lock = asyncio.Lock()

# Step 14: Function to forward token to destination
async def forward_token(token_address):
    """Forward the token address to the destination."""
    try:
        await client.send_message(destination_username, token_address)
        print(f"✅ Forwarded token to destination: {token_address}")
    except Exception as e:
        print(f"❌ Failed to forward token: {e}")

# Step 15: Function to extract data from the message
def extract_data(message_text, token_address, source_age):
    """Extract data from the message text."""
    data = {
        "Token Address": token_address,
        "Token Name": None,
        "Warnings": "N/A",
        "Age": "N/A",
        "Source Age": source_age,  # Source age from @gmgnsignals
        "MC": "N/A",
        "T-MC": "N/A",
        "Liq": "N/A",
        "Liq SOL": "N/A",
        "Vol-1h": "N/A",
        "Price-1h": "N/A",
        "Scans": "N/A",
        "Hodls": "N/A",
        "High": "N/A",
        "Snipers": "N/A",
        "Snipers Percentage": "N/A",
        "Dev": "N/A",
        "Sniped": "N/A",
        "Top Holders": "N/A",
        "Dex": "N/A",
        "LP": "N/A",
        "First": "N/A",
        "First Percentage": "N/A",
        "Sold": "N/A",
        "Burnt": "N/A",
    }

    # Extract Token Name
    token_name_match = re.search(r"\*\*\$([A-Za-z0-9]+)\*\*", message_text)
    if token_name_match:
        data["Token Name"] = token_name_match.group(1)

    # Extract Warnings
    warnings = re.findall(r"(🚨|⚠️)?\s*__([^__]+)__", message_text)
    if warnings:
        filtered_warnings = [
            f"{icon} {warning}" if icon else warning
            for icon, warning in warnings
            if any(keyword.lower() in warning.lower() for keyword in ["High", "Migrating", "Low", "Dev", "Sniped", "Abnormal"])
        ]
        if filtered_warnings:
            data["Warnings"] = " | ".join(filtered_warnings)

    # Extract Age
    age_match = re.search(r"🕒 \*\*Age:\*\* (\d+[smhdw]+\s*\d*[smhdw]*)", message_text)
    if age_match:
        data["Age"] = age_match.group(1)

    # Extract MC
    mc_match = re.search(r"💰 \*\*MC:\*\* (\$\d+\.?\d*[KMB]?)", message_text)
    if mc_match:
        data["MC"] = mc_match.group(1)

    # Extract T-MC
    t_mc_match = re.search(r"🔝 __(\$\d+\.?\d*[KMB]?)__", message_text)
    if t_mc_match:
        data["T-MC"] = t_mc_match.group(1)

    # Extract Liq or vLiq and Liq SOL
    liq_match = re.search(r"💧 \*\*(v?Liq):\*\* (\$\d+\.?\d*[KMB]?) \((\d+\.?\d* SOL)\)", message_text)
    if liq_match:
        liq_type = liq_match.group(1)
        liq_value = liq_match.group(2)
        liq_sol = liq_match.group(3)
        if liq_type == "vLiq":
            liq_value += "-v"
        data["Liq"] = liq_value
        data["Liq SOL"] = liq_sol

    # Extract Vol-1h
    vol_1h_match = re.search(r"\*\*Vol:\*\* __1h__: (\$\d+\.?\d*[KMB]?)", message_text)
    if vol_1h_match:
        data["Vol-1h"] = vol_1h_match.group(1)

    # Extract Price-1h
    price_1h_match = re.search(r"\*\*Price:\*\* __1h__: ([-\d\.]+%)", message_text)
    if price_1h_match:
        data["Price-1h"] = price_1h_match.group(1)

    # Extract Scans
    scans_match = re.search(r"⚡ \*\*Scans:\*\* (\d+)", message_text)
    if scans_match:
        data["Scans"] = scans_match.group(1)

    # Extract Hodls
    hodls_match = re.search(r"\[\*\*Hodls\*\*\]\([^)]+\): (\d+)", message_text)
    if hodls_match:
        data["Hodls"] = hodls_match.group(1)

    # Extract High
    high_match = re.search(r"┗ High:\s*\[(\d+\.?\d*%)]", message_text)
    if high_match:
        data["High"] = high_match.group(1)

    # Extract Snipers and Snipers Percentage
    snipers_match = re.search(r"🔫 \*\*Snipers:\*\* (\d+) • (\d+\.?\d*%)", message_text)
    if snipers_match:
        data["Snipers"] = snipers_match.group(1)
        data["Snipers Percentage"] = snipers_match.group(2)

    # Extract Dev
    dev_match = re.search(r"🛠️ Dev.*?(\d+ SOL)", message_text)
    if dev_match:
        data["Dev"] = dev_match.group(1)

    # Extract Sniped
    sniped_match = re.search(r"┣ Sniped: (\d+\.?\d*%)", message_text)
    if sniped_match:
        data["Sniped"] = sniped_match.group(1)

    # Extract Top Holders
    top_holders_match = re.search(r"\[\*\*Hodls\*\*\]\([^)]+\): \d+ • Top: (\d+\.?\d*%)", message_text)
    if top_holders_match:
        data["Top Holders"] = top_holders_match.group(1)

    # Extract Dex
    dex_match = re.search(r"\[\*\*Dex\*\*\]\([^)]+\)\*\*:\*\* Paid(.+)", message_text)
    if dex_match:
        data["Dex"] = dex_match.group(1).strip()

    # Extract LP
    lp_match = re.search(r"\*\*:\*\* (\d+%) Burnt", message_text)
    if lp_match:
        data["LP"] = lp_match.group(1)

    # Extract First
    first_match = re.search(r"\*\*:\*\* (\d+)\s*Fresh", message_text)
    if first_match:
        data["First"] = first_match.group(1)

    # Extract First Percentage
    first_percentage_match = re.search(r"Fresh\s*•\s*(\d+%)", message_text)
    if first_percentage_match:
        data["First Percentage"] = first_percentage_match.group(1)

    # Extract Sold
    sold_match = re.search(r"\| Sold:\s*(\d+\.?\d*%)", message_text)
    if sold_match:
        data["Sold"] = sold_match.group(1)

    # Extract Burnt
    burnt_match = re.search(r"\| Burnt:\s*(\d+\.?\d*%)", message_text)
    if burnt_match:
        data["Burnt"] = burnt_match.group(1)

    return data

# Step 16: Function to export data to Excel
def export_to_excel(data):
    """Export message data to an Excel file."""
    try:
        wb = load_workbook(EXCEL_FILE)
        ws = wb.active
        ws.append([
            datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            data["Token Address"],
            data["Token Name"],
            data["Warnings"],
            data["Age"],
            data["Source Age"],  # New column
            data["MC"],
            data["T-MC"],
            data["Liq"],
            data["Liq SOL"],
            data["Vol-1h"],
            data["Price-1h"],
            data["Scans"],
            data["Hodls"],
            data["High"],
            data["Snipers"],
            data["Snipers Percentage"],
            data["Dev"],
            data["Sniped"],
            data["Top Holders"],
            data["Dex"],
            data["LP"],
            data["First"],
            data["First Percentage"],
            data["Sold"],
            data["Burnt"],
        ])
        wb.save(EXCEL_FILE)
        print(f"✅ Data exported to {EXCEL_FILE}")
    except Exception as e:
        print(f"❌ Failed to export data to Excel: {e}")

# Step 17: Function to extract token from text
def extract_token_from_text(message_text):
    """Extract token from plain text (last 4 characters must be 'pump')."""
    token_matches = re.findall(token_pattern, message_text)
    if token_matches:
        return token_matches[0]
    return None

# Step 18: Function to extract source age from text
def extract_source_age(message_text):
    """Extract source age from plain text."""
    source_age_match = re.search(source_age_pattern, message_text, re.IGNORECASE)
    if source_age_match:
        return source_age_match.group(1).strip()  # Remove whitespace
    return "N/A"

# Step 19: Function to process one message at a time
async def process_one_message():
    """Fetch and process one message at a time."""
    global last_processed_message_id

    async with processing_lock:
        # Fetch message from @gmgnsignals
        messages = await client.get_messages(source_username, limit=1)
        if not messages:
            print("⚠️ No new messages found.")
            return

        message = messages[0]

        # Skip if already processed
        if last_processed_message_id and message.id <= last_processed_message_id:
            print("⚠️ No new messages to process.")
            return

        last_processed_message_id = message.id
        message_text = message.message

        # Skip non-text messages
        if message_text is None:
            print("⚠️ Message contains no text. Skipping...")
            return

        print(f"📩 New message from @gmgnsignals: {message_text}")

        # Extract Token Address
        token_address = extract_token_from_text(message_text)
        
        # Extract Source Age
        source_age = extract_source_age(message_text)
        print(f"✅ Extracted Source Age: {source_age}")

        # Validation checks
        if not token_address:
            print("⚠️ No token found in the message.")
            return
            
        if token_address in processed_tokens:
            print(f"⚠️ Token already processed: {token_address}")
            return

        # Whitelist/Blacklist checks
        message_text_lower = message_text.lower()
        if whitelist and not any(word.lower() in message_text_lower for word in whitelist):
            print("⚠️ Message does not contain whitelist keywords.")
            return
        if blacklist and any(word.lower() in message_text_lower for word in blacklist):
            print("⚠️ Message contains blacklist keywords.")
            return

        # Forward token to destination bot
        processed_tokens.add(token_address)
        print(f"🎯 Found token: {token_address} | Source Age: {source_age}")

        await forward_token(token_address)
        print(f"⏳ Waiting for {WAIT_TIME} seconds...")
        await asyncio.sleep(WAIT_TIME)

        # Get response from destination bot
        dest_messages = await client.get_messages(destination_username, limit=1)
        if not dest_messages:
            print("⚠️ No response from destination bot.")
            return

        last_dest_message = dest_messages[0]
        print(f"📩 Destination bot response: {last_dest_message.text}")

        # Combine data from both sources
        combined_data = extract_data(last_dest_message.text, token_address, source_age)

        # Export to Excel
        export_to_excel(combined_data)
        print("🛑 Finished processing message.")

# Step 20: Start the client
async def main():
    await client.start(phone_number)
    print("🚀 Bot is running...")

    while True:
        await process_one_message()
        await asyncio.sleep(RATE_LIMIT_DELAY)

# Step 21: Run the bot
if __name__ == "__main__":
    with client:
        client.loop.run_until_complete(main())