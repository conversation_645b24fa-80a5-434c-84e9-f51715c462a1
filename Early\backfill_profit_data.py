"""
Profit Data Recovery Script for Solana Token Tracker

This script takes tokens from tokens.txt and searches @solearlytrending 
to find the latest profit update for each token.
"""

from telethon import TelegramClient
from telethon.tl.types import MessageEntityTextUrl, MessageEntityUrl
import re
import asyncio
import os
import time
from openpyxl import load_workbook
from openpyxl.styles import PatternFill
from datetime import datetime, timedelta
import logging
import sys

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Import functions from main script
from early import (
    extract_urls_from_entities, 
    extract_profit_from_message,
    API_ID, 
    API_HASH, 
    PHONE, 
    EXCEL_FILE,
    HEADERS,
    GREEN_FILL,
    BLUE_FILL,
    ORANGE_FILL,
    RED_FILL,
    PURPLE_FILL,
    YELLOW_FILL,
    GREY_FILL
)

# Constants
MAX_MESSAGES = 5000  # Maximum number of messages to process
RATE_LIMIT = 0.1  # Faster request rate
HISTORY_DAYS = 60  # Look further back for profit messages
TOKENS_FILE = "tokens.txt"  # File containing tokens to search

# Channel for profit data
PROFIT_CHANNEL = "solearlytrending"

async def load_tokens_from_file():
    """Load token addresses from the tokens.txt file"""
    tokens = []
    
    if not os.path.exists(TOKENS_FILE):
        logger.error(f"Tokens file not found: {TOKENS_FILE}")
        return []
        
    with open(TOKENS_FILE, 'r') as f:
        for line in f:
            line = line.strip()
            # Skip comments and empty lines
            if line and not line.startswith('#') and len(line) >= 43:
                tokens.append(line)
    
    logger.info(f"Loaded {len(tokens)} tokens from {TOKENS_FILE}")
    return tokens

async def check_token_in_excel(token):
    """Check if token exists in Excel and return True/False"""
    if not os.path.exists(EXCEL_FILE):
        return False
        
    wb = load_workbook(EXCEL_FILE)
    ws = wb.active
    
    # Find token address column
    token_col_idx = None
    for i, cell in enumerate(ws[1], 1):
        if cell.value == "Token Address":
            token_col_idx = i
            break
    
    if not token_col_idx:
        return False
    
    # Check if token exists
    for row in ws.iter_rows(min_row=2, values_only=True):
        if row[token_col_idx-1] == token:
            return True
    
    return False

async def update_excel_with_profit(token_address, profit_value):
    """Update Excel with profit data for the token"""
    try:
        if not profit_value:
            logger.warning(f"No profit data for {token_address}")
            return False
            
        wb = load_workbook(EXCEL_FILE)
        ws = wb.active
        
        # Find profit and token columns
        profit_col_idx = None
        token_col_idx = None
        
        for i, cell in enumerate(ws[1], 1):
            if cell.value == "Profit":
                profit_col_idx = i
            elif cell.value == "Token Address":
                token_col_idx = i
        
        if not profit_col_idx or not token_col_idx:
            logger.error("Could not find Profit or Token Address columns")
            return False
        
        # Look for the token
        token_row = None
        
        for row_idx in range(2, ws.max_row + 1):
            if ws.cell(row=row_idx, column=token_col_idx).value == token_address:
                token_row = row_idx
                break
        
        if token_row:
            # Update profit value
            ws.cell(row=token_row, column=profit_col_idx, value=profit_value)
            cell = ws.cell(row=token_row, column=profit_col_idx)
            
            # Apply formatting based on profit value
            try:
                if '%' in profit_value:
                    profit_num = float(profit_value.replace('%', '').strip())
                    if 50 <= profit_num < 100:
                        cell.fill = GREY_FILL
                elif 'X' in profit_value:
                    profit_num = float(profit_value.replace('X', '').strip())
                    if 2 <= profit_num < 3:
                        cell.fill = YELLOW_FILL
                    elif 3 <= profit_num < 4:
                        cell.fill = PURPLE_FILL
                    elif 5 <= profit_num < 6:
                        cell.fill = RED_FILL
                    elif 7 <= profit_num < 8:
                        cell.fill = ORANGE_FILL
                    elif 10 <= profit_num < 20:
                        cell.fill = GREEN_FILL
                    elif profit_num >= 20:
                        cell.fill = BLUE_FILL
            except ValueError:
                logger.warning(f"Could not parse profit value: {profit_value}")
            
            # Save workbook
            wb.save(EXCEL_FILE)
            logger.info(f"Updated profit to {profit_value} for token {token_address}")
            return True
        else:
            logger.warning(f"Token {token_address} not found in Excel")
            return False
            
    except Exception as e:
        logger.error(f"Error updating Excel for {token_address}: {e}")
        return False

async def find_profit_updates():
    """Find the latest profit updates for tokens in the solearlytrending channel"""
    # Connect to Telegram
    client = TelegramClient("profit_data_recovery", API_ID, API_HASH)
    await client.start(PHONE)
    
    try:
        # Load tokens from file
        tokens = await load_tokens_from_file()
        if not tokens:
            logger.error("No tokens found in tokens file. Exiting.")
            return
            
        # Calculate date cutoff
        cutoff_date = datetime.now() - timedelta(days=HISTORY_DAYS)
        
        # Get historical messages from solearlytrending
        logger.info(f"Fetching up to {MAX_MESSAGES} messages from {PROFIT_CHANNEL}")
        messages = await client.get_messages(
            PROFIT_CHANNEL, 
            limit=MAX_MESSAGES,
            offset_date=cutoff_date
        )
        logger.info(f"Retrieved {len(messages)} messages from {PROFIT_CHANNEL}")
        
        # First pass: organize all profit messages by token
        logger.info("Processing profit messages...")
        token_profits = {}  # Map tokens to their profit data
        token_dates = {}    # Track the message dates to find the latest
        
        for msg in messages:
            if not hasattr(msg, 'text') or not msg.text:
                continue
                
            # Look for "is up" messages
            if "is up" in msg.text.lower():
                # Extract profit value
                profit_value = extract_profit_from_message(msg.text)
                if not profit_value:
                    continue
                
                # Look for token addresses in the message
                found_tokens = set()
                
                # Direct token mentions in message text
                direct_tokens = re.findall(r'\b([A-Za-z0-9]{43,44})\b', msg.text)
                found_tokens.update(direct_tokens)
                
                # URLs containing tokens
                urls = extract_urls_from_entities(msg)
                for url in urls:
                    # Check for GeckoTerminal URLs which often contain token addresses
                    token_match = re.search(r'geckoterminal\.com/solana/pools/([a-zA-Z0-9]+)', url, re.IGNORECASE)
                    if token_match:
                        found_tokens.add(token_match.group(1))
                
                # Check if any of our target tokens are in this message
                for token in tokens:
                    if token in found_tokens or token in msg.text:
                        # If we already have a date for this token, compare them
                        if token in token_dates:
                            # Only keep the newer one
                            if msg.date > token_dates[token]:
                                token_profits[token] = profit_value
                                token_dates[token] = msg.date
                                logger.info(f"Found newer profit {profit_value} for {token} from {msg.date}")
                        else:
                            # First time seeing this token
                            token_profits[token] = profit_value
                            token_dates[token] = msg.date
                            logger.info(f"Found profit {profit_value} for {token} from {msg.date}")
        
        # Process results and update Excel
        logger.info(f"Found profit updates for {len(token_profits)} tokens")
        tokens_updated = 0
        
        for token, profit in token_profits.items():
            if await update_excel_with_profit(token, profit):
                tokens_updated += 1
            
            # Rate limiting
            await asyncio.sleep(RATE_LIMIT)
            
            # Progress updates
            if tokens_updated % 10 == 0 and tokens_updated > 0:
                logger.info(f"Progress: Updated {tokens_updated} tokens with profit data")
        
        logger.info(f"Completed updating {tokens_updated} tokens with profit data")
        
    except Exception as e:
        logger.error(f"Error in finding profit updates: {e}")
        import traceback
        logger.error(traceback.format_exc())
    finally:
        await client.disconnect()

if __name__ == "__main__":
    # Run the profit recovery script
    asyncio.run(find_profit_updates())