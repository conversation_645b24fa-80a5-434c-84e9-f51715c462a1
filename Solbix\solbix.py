from telethon import Telegram<PERSON>lient, events
from telethon.tl.types import MessageEntityTextUrl, MessageEntityUrl
from telethon.errors import <PERSON><PERSON>ait<PERSON>rror, ServerError, TimedOutError, RPCError
from telethon.sessions import StringSession
import re
import asyncio
import os
import time
import pickle
import pytz
import sys
import logging
from openpyxl import Workbook, load_workbook
from openpyxl.styles import Pat<PERSON><PERSON>ill
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Step 1: Configuration settings
# Configuration settings
API_ID = "24841946"
API_HASH = "38d74f00caf1381396fc58a579e89a97"
PHONE = "+212762663516"

# Directly use credentials
api_id = API_ID
api_hash = API_HASH
phone_number = PHONE

# Step 2: Source and Destination IDs
source_username = "@csolbix"  # Username of the source channel
destination_username = "@soul_scanner_bot"  # Username of the first destination bot
destination_username2 = "@TokenTrackerbotbot_bot"  # Username of the second destination bot
destination_username3 = "@WizSarumanBot"  # Username of the third destination bot
destination_username4 = "@maestro"  # Username of the fourth destination bot
destination_username5 = "@DbotxSIMBot"  # Username of the fifth destination bot (with /buy command)
destination_username6 = "@TrailingDbotx_bot"  # Username of the sixth destination bot

# Step 3: Regex patterns for token extraction
token_pattern_type1 = r"\(https?://(?:app\.)?bubblemaps\.io/sol/token/([A-Za-z0-9]{43,44})"  # Type 1 pattern - extract token from Bubblemap URL in parentheses
token_pattern_type2 = r"https://xray\.helius\.xyz/token/([A-Za-z0-9]{43,44})"  # Updated Type 2 token pattern for Helius XRay URLs

# Step 4: Message filtering patterns
type1_pattern = ["Make a call here"]  # Process Type 1 messages containing these words
type2_pattern = ["is up", "has dropped"]  # Process Type 2 messages containing these words

# Step 5: Rate Limiting (in seconds)
RATE_LIMIT_DELAY = 3  # Set to 2 seconds for balanced speed and reliability
MAX_RETRIES = 8  # Increased for better reliability
CATCH_UP_MESSAGES = 100  # Increased number of past messages to fetch on startup
KEEPALIVE_INTERVAL = 30  # Check connection every 30 seconds instead of 60
CONNECTION_CHECK_INTERVAL = 5  # Check if we're receiving messages every 5 minutes
MISSED_MESSAGE_THRESHOLD = 300  # If no messages received for 5 minutes, refetch recent messages

# Step 6: Excel file setup with combined columns (including Profit column)
EXCEL_FILE = "solbix_data.xlsx"
HEADERS = [
    "Timestamp", "Token Address", "Token Name", "Called By", "Calls", "Profit", "Trailing Sell", "Made", "Fish", "Bonding Curve", "X URL", "Warnings", "Age", "MC", "T-MC", "Liq", "Liq SOL",
    "Scans", "Hodls", "High", "Snipers", "Snipers Percentage", "Dev", "Sniped",
    "Top Holders", "LP", "First", "First Percentage", "Sold"
]

# Step 7: Conditional formatting colors
# Using hex colors without alpha channel for better compatibility
GREY_FILL = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")    # 50% - 99%
YELLOW_FILL = PatternFill(start_color="FFFF00", end_color="FFFF00", fill_type="solid")  # 100% - 299%
PURPLE_FILL = PatternFill(start_color="800080", end_color="800080", fill_type="solid")  # 300% - 499%
ORANGE_FILL = PatternFill(start_color="FFA500", end_color="FFA500", fill_type="solid")  # 500% - 999%
GREEN_FILL = PatternFill(start_color="00FF00", end_color="00FF00", fill_type="solid")   # 1000% - 1999%
BLUE_FILL = PatternFill(start_color="0000FF", end_color="0000FF", fill_type="solid")    # 2000% - 10000%
RED_FILL = PatternFill(start_color="FF0000", end_color="FF0000", fill_type="solid")     # below -35%

# Step 8: Target group configurations have been removed

# Define Morocco timezone
MOROCCO_TZ = pytz.timezone('Africa/Casablanca')

# Session file for persistence
SESSION_FILE = "session_state.pickle"
SESSION_STRING_FILE = "session_string.txt"

# Initialize important global variables
processed_tokens = set()
processing_lock = asyncio.Lock()
processing_queue = asyncio.Queue()
message_buffer = []  # Buffer for messages when rate limited
is_processing = False
client = None  # Will be initialized in main()
last_message_time = time.time()  # Track when we last received a message
update_state = {"pts": None, "qts": None, "date": None, "seq": None}  # For tracking updates

# Initialize Excel file if it doesn't exist
def initialize_excel():
    """Create Excel file with headers if it doesn't exist"""
    if not os.path.exists(EXCEL_FILE):
        logger.info(f"Creating new Excel file: {EXCEL_FILE}")
        wb = Workbook()
        ws = wb.active
        ws.append(HEADERS)
        wb.save(EXCEL_FILE)
    else:
        # Check if required columns exist, if not add them
        wb = load_workbook(EXCEL_FILE)
        ws = wb.active
        headers = [cell.value for cell in ws[1]]

        # Check for Profit column
        if "Profit" not in headers:
            logger.info("Adding Profit column to existing Excel file")
            profit_col_idx = len(headers) + 1
            ws.cell(row=1, column=profit_col_idx, value="Profit")
            wb.save(EXCEL_FILE)

        # Check for Trailing Sell column
        if "Trailing Sell" not in headers:
            logger.info("Adding Trailing Sell column to existing Excel file")
            # Find the position after Profit
            if "Profit" in headers:
                profit_idx = headers.index("Profit")
                # Insert a new column after Profit
                ws.insert_cols(profit_idx + 2)
                # Add the header
                ws.cell(row=1, column=profit_idx + 2, value="Trailing Sell")
                # Fill all existing rows with "N/A"
                for row_idx in range(2, ws.max_row + 1):
                    ws.cell(row=row_idx, column=profit_idx + 2, value="N/A")
            else:
                # If Profit doesn't exist, just add it at the end
                trailing_sell_col_idx = len(headers) + 1
                ws.cell(row=1, column=trailing_sell_col_idx, value="Trailing Sell")

            wb.save(EXCEL_FILE)

        # Check for Called By column
        if "Called By" not in headers:
            logger.info("Adding Called By column to existing Excel file")
            # Find the position after Token Name
            if "Token Name" in headers:
                token_name_idx = headers.index("Token Name")
                # Insert a new column after Token Name
                ws.insert_cols(token_name_idx + 2)
                # Add the header
                ws.cell(row=1, column=token_name_idx + 2, value="Called By")
                # Fill all existing rows with "N/A"
                for row_idx in range(2, ws.max_row + 1):
                    ws.cell(row=row_idx, column=token_name_idx + 2, value="N/A")
            else:
                # If Token Name doesn't exist, just add it at the end
                called_by_col_idx = len(headers) + 1
                ws.cell(row=1, column=called_by_col_idx, value="Called By")

            # Reload headers after adding Called By column
            headers = [cell.value for cell in ws[1]]
            wb.save(EXCEL_FILE)

        # Check for Calls column
        if "Calls" not in headers:
            logger.info("Adding Calls column to existing Excel file")
            # Find the position after Called By
            if "Called By" in headers:
                called_by_idx = headers.index("Called By")
                # Insert a new column after Called By
                ws.insert_cols(called_by_idx + 2)
                # Add the header
                ws.cell(row=1, column=called_by_idx + 2, value="Calls")
                # Fill all existing rows with "N/A"
                for row_idx in range(2, ws.max_row + 1):
                    ws.cell(row=row_idx, column=called_by_idx + 2, value="N/A")
            else:
                # If Called By doesn't exist, just add it at the end
                calls_col_idx = len(headers) + 1
                ws.cell(row=1, column=calls_col_idx, value="Calls")

            wb.save(EXCEL_FILE)

def clear_terminal():
    """Clear the terminal screen."""
    os.system("cls" if os.name == "nt" else "clear")

def save_session_state():
    """Save the current session state to a file."""
    try:
        with open(SESSION_FILE, 'wb') as f:
            pickle.dump({
                'update_state': update_state,
                'processed_tokens': processed_tokens
            }, f)
        logger.info(f"✅ Session state saved to {SESSION_FILE}")

        # Also save the session string if available
        if client and hasattr(client, 'session') and isinstance(client.session, StringSession):
            with open(SESSION_STRING_FILE, 'w') as f:
                f.write(client.session.save())
            logger.info(f"✅ Session string saved to {SESSION_STRING_FILE}")
    except Exception as e:
        logger.error(f"❌ Failed to save session state: {e}")

def load_session_state():
    """Load the session state from a file."""
    global update_state, processed_tokens
    try:
        if os.path.exists(SESSION_FILE):
            with open(SESSION_FILE, 'rb') as f:
                data = pickle.load(f)
                update_state = data.get('update_state', update_state)
                processed_tokens = data.get('processed_tokens', processed_tokens)
            logger.info(f"✅ Session state loaded from {SESSION_FILE}")
            return True
        return False
    except Exception as e:
        logger.error(f"❌ Failed to load session state: {e}")
        return False

def should_forward_token(data):
    """Custom filter function with all specified criteria"""
    try:
        # All filters removed - always return True
        return True
    except Exception as e:
        logger.error(f"❌ Error in should_forward_token: {e}")
        return True

# detect_keywords function has been removed

# search_token_in_groups function has been removed

def extract_data_from_scanner_response(message_text, token_address):
    """Extract data from the scanner bot's message."""
    data = {
        "Token Address": token_address,
        "Token Name": "N/A",
        "Called By": "N/A",
        "Calls": "N/A",
        "Profit": "Lost",
        "Trailing Sell": "N/A",
        "Made": "N/A",
        "Fish": "N/A",
        "Bonding Curve": "N/A",
        "X URL": "N/A",
        "Warnings": "N/A",
        "Age": "N/A",
        "MC": "N/A",
        "T-MC": "N/A",
        "Liq": "N/A",
        "Liq SOL": "N/A",
        "Scans": "N/A",
        "Hodls": "N/A",
        "High": "N/A",
        "Snipers": "N/A",
        "Snipers Percentage": "N/A",
        "Dev": "N/A",
        "Sniped": "N/A",
        "Top Holders": "N/A",
        "LP": "N/A",
        "First": "N/A",
        "First Percentage": "N/A",
        "Sold": "N/A"
    }

    # Token Name
    token_name_match = re.search(r"\*\*\$([A-Za-z0-9]+)\*\*", message_text)
    if token_name_match:
        data["Token Name"] = token_name_match.group(1)

    # Warnings
    warnings = re.findall(r"(🚨|⚠️)?\s*__([^__]+)__", message_text)
    if warnings:
        filtered_warnings = [
            f"{icon} {warning}" if icon else warning
            for icon, warning in warnings
            if any(keyword.lower() in warning.lower()
                 for keyword in ["High", "Migrating", "Low", "Dev", "Sniped", "Abnormal"])
        ]
        if filtered_warnings:
            data["Warnings"] = " | ".join(filtered_warnings)

    # Age
    age_match = re.search(r"🕒 \*\*Age:\*\* (\d+[smhdw]+\s*\d*[smhdw]*)", message_text)
    if age_match:
        data["Age"] = age_match.group(1)

    # Market Cap
    mc_match = re.search(r"💰 \*\*MC:\*\* (\$\d[\d,.]*[KMB]?)", message_text)
    if mc_match:
        data["MC"] = mc_match.group(1)

    # T-MC
    t_mc_match = re.search(r"🔝 __(\$\d[\d,.]*[KMB]?)__", message_text)
    if t_mc_match:
        data["T-MC"] = t_mc_match.group(1)

    # Liquidity
    liq_match = re.search(r"💧 \*\*(v?Liq):\*\* (\$\d[\d,.]*[KMB]?) \((\d+\.?\d* SOL)\)", message_text)
    if liq_match:
        liq_type = liq_match.group(1)
        liq_value = liq_match.group(2)
        liq_sol = liq_match.group(3)
        if liq_type == "vLiq":
            liq_value += "-v"
        data["Liq"] = liq_value
        data["Liq SOL"] = liq_sol

    # Scans
    scans_match = re.search(r"⚡ \*\*Scans:\*\* (\d+)", message_text)
    if scans_match:
        data["Scans"] = scans_match.group(1)

    # Hodls
    hodls_match = re.search(r"\[\*\*Hodls\*\*\]\([^)]+\): (\d+)", message_text)
    if hodls_match:
        data["Hodls"] = hodls_match.group(1)

    # High
    high_match = re.search(r"┗ High:\s*\[(\d+\.?\d*%)]", message_text)
    if high_match:
        data["High"] = high_match.group(1)

    # Snipers
    snipers_match = re.search(r"🔫 \*\*Snipers:\*\* (\d+) • (\d+\.?\d*%)", message_text)
    if snipers_match:
        data["Snipers"] = snipers_match.group(1)
        data["Snipers Percentage"] = snipers_match.group(2)

    # Dev
    dev_match = re.search(r"🛠️ Dev.*?(\d+ SOL)", message_text)
    if dev_match:
        data["Dev"] = dev_match.group(1)

    # Sniped
    sniped_match = re.search(r"┣ Sniped: (\d+\.?\d*%)", message_text)
    if sniped_match:
        data["Sniped"] = sniped_match.group(1)

    # Top Holders
    top_holders_match = re.search(r"\[\*\*Hodls\*\*\]\([^)]+\): \d+ • Top: (\d+\.?\d*%)", message_text)
    if top_holders_match:
        data["Top Holders"] = top_holders_match.group(1)

    # LP
    lp_match = re.search(r"\*\*:\*\* (\d+%) Burnt", message_text)
    if lp_match:
        data["LP"] = lp_match.group(1)

    # First
    first_match = re.search(r"\*\*:\*\* (\d+)\s*Fresh", message_text)
    if first_match:
        data["First"] = first_match.group(1)

    # First Percentage
    first_percentage_match = re.search(r"Fresh\s*•\s*(\d+%)", message_text)
    if first_percentage_match:
        data["First Percentage"] = first_percentage_match.group(1)

    # Sold
    sold_match = re.search(r"\| Sold:\s*(\d+\.?\d*%)", message_text)
    if sold_match:
        data["Sold"] = sold_match.group(1)

    # Burnt
    burnt_match = re.search(r"\| Burnt:\s*(\d+\.?\d*%)", message_text)
    if burnt_match:
        data["Burnt"] = burnt_match.group(1)


    # Made (updated pattern)
    made_match = re.search(r"Made:\s*(\d+)", message_text)
    if made_match:
        data["Made"] = made_match.group(1)


    # First Fish/Symbol
    fish_match = re.search(r"First[^\|]+\|\s*([\d,]+ [^\d\s•]+)", message_text)
    if fish_match:
        data["Fish"] = fish_match.group(1)
    return data

async def export_to_excel(data):
    """Export combined data to Excel with thread-safety."""
    async with processing_lock:
        try:
            wb = load_workbook(EXCEL_FILE)
            ws = wb.active

            # Special case for Trailing Stop Loss updates where we only have the token name
            if "Token Name" in data and "Trailing Sell" in data and "Token Address" not in data:
                token_name = data.get("Token Name")
                trailing_sell = data.get("Trailing Sell")

                if not token_name or not trailing_sell:
                    logger.warning("⚠️ Missing token name or trailing sell value for Excel export")
                    return

                # Find the token by name
                token_name_col_idx = HEADERS.index("Token Name") + 1
                trailing_sell_col_idx = HEADERS.index("Trailing Sell") + 1

                # Search for the token by name
                found = False
                for row_idx in range(2, ws.max_row + 1):
                    cell_value = ws.cell(row=row_idx, column=token_name_col_idx).value
                    if cell_value and cell_value.lower() == token_name.lower():
                        # Found the token, update the trailing sell value
                        ws.cell(row=row_idx, column=trailing_sell_col_idx, value=trailing_sell)
                        found = True
                        logger.info(f"✅ Updated trailing sell for token {token_name} in row {row_idx}")
                        break

                if not found:
                    logger.warning(f"⚠️ Token {token_name} not found in Excel file")
                    return

                # Save the workbook
                wb.save(EXCEL_FILE)
                logger.info(f"💾 Excel file saved with trailing sell update for {token_name}")
                return

            # Regular case - check if the token address already exists in the Excel file
            token_exists = False
            token_row = None

            for row_idx, row in enumerate(ws.iter_rows(min_row=2, values_only=True), start=2):
                if row[1] == data.get("Token Address"):  # Token Address is in column 2
                    token_exists = True
                    token_row = row_idx
                    break

            if token_exists:
                # Check if this is just a profit update (Type 2 message)
                if len(data.keys()) <= 3 and "Profit" in data and data.get("Profit") != "N/A":
                    # This is just a profit update, just update the profit column
                    profit_col_idx = HEADERS.index("Profit") + 1
                    ws.cell(row=token_row, column=profit_col_idx, value=data["Profit"])
                    cell = ws.cell(row=token_row, column=profit_col_idx)

                    if '%' in data["Profit"]:
                        # Remove the % sign and convert to float
                        profit_str = data["Profit"].rstrip('%')
                        # Handle negative values
                        if profit_str.startswith('-'):
                            profit_value = float(profit_str)
                            # Below -35% with color red
                            if profit_value <= -35:
                                cell.fill = RED_FILL
                        else:
                            profit_value = float(profit_str)
                            # 50% - 99% with color grey
                            if 50 <= profit_value < 100:
                                cell.fill = GREY_FILL
                            # 100% - 299% with color yellow
                            elif 100 <= profit_value < 300:
                                cell.fill = YELLOW_FILL
                            # 300% - 499% with color purple
                            elif 300 <= profit_value < 500:
                                cell.fill = PURPLE_FILL
                            # 500% - 999% with color orange
                            elif 500 <= profit_value < 1000:
                                cell.fill = ORANGE_FILL
                            # 1000% - 1999% with color green
                            elif 1000 <= profit_value < 2000:
                                cell.fill = GREEN_FILL
                            # 2000% - 10000% with color blue
                            elif 2000 <= profit_value <= 10000:
                                cell.fill = BLUE_FILL

                    # Update X URL if provided
                    if "X URL" in data and data["X URL"] != "N/A":
                        x_url_col_idx = HEADERS.index("X URL") + 1
                        ws.cell(row=token_row, column=x_url_col_idx, value=data["X URL"])

                    logger.info(f"✅ Updated profit data for existing token {data['Token Address']}")
                elif "Token Name" in data and data["Token Name"] != "N/A":
                    # This is a full token data update from scanner bot, skip if token already exists
                    logger.info(f"⏩ Token {data['Token Address']} already exists, skipping scanner data update")
                else:
                    # This is a partial update with fields other than profit, apply them
                    for col_idx, field in enumerate(HEADERS[1:], start=2):
                        if field in data and data[field] != "N/A":
                            ws.cell(row=token_row, column=col_idx, value=data[field])

                    logger.info(f"✅ Updated specific fields for existing token {data['Token Address']}")
            else:
                # Use Morocco time for timestamp
                morocco_time = datetime.now(MOROCCO_TZ).strftime("%Y-%m-%d %H:%M:%S")
                row = [
                    morocco_time,
                    data["Token Address"],
                    data["Token Name"],
                    data["Called By"],
                    data["Calls"],
                    data["Profit"],
                    data["Trailing Sell"],
                    data["Made"],
                    data["Fish"],
                    data["Bonding Curve"],
                    data["X URL"],
                    data["Warnings"],
                    data["Age"],
                    data["MC"],
                    data["T-MC"],
                    data["Liq"],
                    data["Liq SOL"],
                    data["Scans"],
                    data["Hodls"],
                    data["High"],
                    data["Snipers"],
                    data["Snipers Percentage"],
                    data["Dev"],
                    data["Sniped"],
                    data["Top Holders"],
                    data["LP"],
                    data["First"],
                    data["First Percentage"],
                    data["Sold"]
                ]
                ws.append(row)
                logger.info(f"✅ Added new row for token {data['Token Address']}")

                # Apply conditional formatting to the profit cell for new rows
                if "Profit" in data and data["Profit"] != "N/A" and '%' in data["Profit"]:
                    # Get the row number of the newly added row
                    new_row_idx = ws.max_row
                    # Get the column index of the Profit column
                    profit_col_idx = HEADERS.index("Profit") + 1
                    # Get the cell
                    cell = ws.cell(row=new_row_idx, column=profit_col_idx)

                    # Remove the % sign and convert to float
                    profit_str = data["Profit"].rstrip('%')
                    # Handle negative values
                    if profit_str.startswith('-'):
                        profit_value = float(profit_str)
                        # Below -35% with color red
                        if profit_value <= -35:
                            cell.fill = RED_FILL
                    else:
                        profit_value = float(profit_str)
                        # 50% - 99% with color grey
                        if 50 <= profit_value < 100:
                            cell.fill = GREY_FILL
                        # 100% - 299% with color yellow
                        elif 100 <= profit_value < 300:
                            cell.fill = YELLOW_FILL
                        # 300% - 499% with color purple
                        elif 300 <= profit_value < 500:
                            cell.fill = PURPLE_FILL
                        # 500% - 999% with color orange
                        elif 500 <= profit_value < 1000:
                            cell.fill = ORANGE_FILL
                        # 1000% - 1999% with color green
                        elif 1000 <= profit_value < 2000:
                            cell.fill = GREEN_FILL
                        # 2000% - 10000% with color blue
                        elif 2000 <= profit_value <= 10000:
                            cell.fill = BLUE_FILL

            wb.save(EXCEL_FILE)
            logger.info(f"✅ Data saved to {EXCEL_FILE}")
        except Exception as e:
            logger.error(f"❌ Failed to export data to Excel: {e}")
            raise

def extract_urls_from_entities(message):
    """Extract URLs and text content from message entities."""
    urls = []
    if message.entities:
        for entity in message.entities:
            # Extract URLs from URL entities
            if isinstance(entity, (MessageEntityTextUrl, MessageEntityUrl)):
                url = entity.url if isinstance(entity, MessageEntityTextUrl) else message.text[entity.offset:entity.offset + entity.length]
                urls.append(url)

            # Extract text content from all entities (might contain token addresses)
            if hasattr(entity, 'offset') and hasattr(entity, 'length'):
                entity_text = message.text[entity.offset:entity.offset + entity.length]
                # Only add if it looks like a token address (43-44 characters of alphanumeric)
                if re.match(r'^[A-Za-z0-9]{43,44}$', entity_text):
                    urls.append(entity_text)
    return urls

def extract_token_from_url(url, pattern):
    """Extract token from URL using the specified regex pattern."""
    if not url:
        return None

    # First try the provided pattern
    token_matches = re.findall(pattern, url)
    if token_matches:
        return token_matches[0]

    # Special handling for Bubblemap URLs in parentheses
    bubblemap_match = re.search(r'\(https?://(?:app\.)?bubblemaps\.io/sol/token/([A-Za-z0-9]{43,44})', url)
    if bubblemap_match:
        return bubblemap_match.group(1)

    return None

def extract_caller_from_message(message_text):
    """Extract the caller's name from a message that starts with 'Call by'."""
    if not message_text:
        return "N/A"

    # Match pattern like "Call by @username - ..." or "[**Call by @username - ..."
    caller_match = re.search(r'\[\*\*Call by @([A-Za-z0-9_]+)', message_text)
    if not caller_match:
        caller_match = re.search(r'Call by @([A-Za-z0-9_]+)', message_text)

    # If we found a match with @ symbol
    if caller_match:
        return f"@{caller_match.group(1)}"

    # Match pattern like "Call by 6793...3178 - ..." or "[**Call by 6793...3178 - ..."
    # This pattern matches numeric IDs with or without ellipsis
    numeric_match = re.search(r'\[\*\*Call by ([0-9]+(?:\.{3}[0-9]+)?)', message_text)
    if not numeric_match:
        numeric_match = re.search(r'Call by ([0-9]+(?:\.{3}[0-9]+)?)', message_text)

    # If we found a match with numeric ID
    if numeric_match:
        return numeric_match.group(1)

    return "N/A"

def extract_calls_from_message(message_text):
    """Extract the number of calls from a message that contains '- X calls -'."""
    if not message_text:
        return "N/A"

    # Match pattern like "- 5 calls -" or "- 19 calls -"
    calls_match = re.search(r'-\s*(\d+)\s*calls\s*-', message_text)

    # If we found a match
    if calls_match:
        return calls_match.group(1)

    return "N/A"

def extract_trailing_sell_from_message(message_text):
    """Extract the Current P/L percentage from a Trailing Stop Loss message."""
    if not message_text:
        return "N/A"

    # Check if this is a Trailing Stop Loss message
    if "Trailing Stop Loss Triggered" not in message_text:
        return "N/A"

    # Match pattern like "Current P/L: +60.00%" or "Current P/L: -25.50%"
    pl_match = re.search(r'Current P/L:\s*([+-]\d+\.\d+%)', message_text)

    # If we found a match
    if pl_match:
        return pl_match.group(1)

    return "N/A"

def extract_profit_from_message(message_text):
    """Extract profit value from Type 2 message with enhanced pattern matching."""
    if not message_text:
        return None

    # 1. Match standard format: "is up 53%" or "is up 5.3X" with flexible spacing/formatting and punctuation
    profit_match = re.search(r"is\s+up\s*[^\d]*(\d+\.?\d*)([%Xx])[!.]*", message_text, re.IGNORECASE)
    if profit_match:
        value = profit_match.group(1)
        unit = profit_match.group(2).upper()  # Normalize unit to uppercase

        # Convert X to percentage (e.g., 2X -> 200%)
        if unit == 'X':
            value_float = float(value)
            percentage = value_float * 100
            return f"{percentage:.0f}%"
        else:
            return f"{value}{unit}"

    # 1b. Match "has dropped" format: "has dropped 25%" or "has dropped -35%" with flexible spacing/formatting
    drop_match = re.search(r"has\s+dropped\s*[^\d\-]*(-?\d+\.?\d*)([%Xx])[!.]*", message_text, re.IGNORECASE)
    if drop_match:
        value = drop_match.group(1)
        unit = drop_match.group(2).upper()  # Normalize unit to uppercase

        # Convert X to percentage if needed
        if unit == 'X':
            # Remove negative sign if present, convert to percentage, then add negative sign back
            if value.startswith('-'):
                value_float = float(value[1:])  # Remove negative sign for calculation
                percentage = value_float * 100
                return f"-{percentage:.0f}%"  # Add negative sign back
            else:
                value_float = float(value)
                percentage = value_float * 100
                return f"-{percentage:.0f}%"  # Always add negative sign for drops
        else:
            # For percentage values, ensure they have a negative sign
            if value.startswith('-'):
                return f"{value}{unit}"  # Already has negative sign
            else:
                return f"-{value}{unit}"  # Add negative sign

    # 2. Try arrow notation format: "$50.6K —> $77.1K 💵" with flexible arrow styles
    mc_match = re.search(r"\$(\d+\.?\d*)([KMB])?\s*(?:—>|->|=>|⇒)\s*\$(\d+\.?\d*)([KMB])?", message_text)
    if mc_match:
        # Extract values and convert to float
        start_value = float(mc_match.group(1))
        start_unit = mc_match.group(2) or ''
        end_value = float(mc_match.group(3))
        end_unit = mc_match.group(4) or ''

        # Convert to same units
        multipliers = {'': 1, 'K': 1000, 'M': 1000000, 'B': 1000000000}
        start_amount = start_value * multipliers[start_unit]
        end_amount = end_value * multipliers[end_unit]

        # Calculate percentage increase or decrease
        if end_amount > start_amount:
            # This is an increase
            increase_ratio = end_amount / start_amount
            # Always convert to percentage format
            percentage = (increase_ratio - 1) * 100
            return f"{percentage:.0f}%"
        elif end_amount < start_amount:
            # This is a decrease
            percentage = (1 - (end_amount / start_amount)) * 100
            return f"-{percentage:.0f}%"  # Add negative sign for decreases

    # 3. Look for a specific pattern with the words "up" and percentage/X value near each other
    up_val_match = re.search(r"up\s+(?:by\s+)?(-?\d+\.?\d*)([%Xx])[!.]*", message_text, re.IGNORECASE)
    if up_val_match:
        value = up_val_match.group(1)
        unit = up_val_match.group(2).upper()

        # Convert X to percentage
        if unit == 'X':
            value_float = float(value.replace('-', ''))  # Remove negative sign if present
            percentage = value_float * 100

            # If there was a negative sign, preserve it
            if value.startswith('-'):
                return f"-{percentage:.0f}%"
            else:
                return f"{percentage:.0f}%"
        else:
            return f"{value}{unit}"

    # 4. Look for standalone values that might represent profits
    # This is the most aggressive matching and only use if nothing else works
    profit_indicators = ["profit", "gain", "up", "increased", "jumped", "gained"]
    loss_indicators = ["dropped", "loss", "down", "decreased", "fell", "lost"]

    # Check if this is likely a profit or loss message
    is_profit = any(indicator in message_text.lower() for indicator in profit_indicators)
    is_loss = any(indicator in message_text.lower() for indicator in loss_indicators)

    if is_profit or is_loss:
        # Look for percentage or X values, including those with negative signs and punctuation
        standalone_match = re.search(r"(-?\d+\.?\d*)([%Xx])[!.]*", message_text)
        if standalone_match:
            value = standalone_match.group(1)
            unit = standalone_match.group(2).upper()

            # Convert X to percentage if needed
            if unit == 'X':
                value_float = float(value.replace('-', ''))  # Remove negative sign if present
                percentage = value_float * 100

                # If it's a loss message, ensure it has a negative sign
                if is_loss:
                    return f"-{percentage:.0f}%"
                else:
                    # For profit messages, keep as positive
                    return f"{percentage:.0f}%"
            else:
                # For percentage values
                if is_loss:
                    # Ensure loss has a negative sign
                    if value.startswith('-'):
                        return f"{value}{unit}"  # Already has negative sign
                    else:
                        return f"-{value}{unit}"  # Add negative sign
                else:
                    # For profit messages, keep as is (positive)
                    return f"{value}{unit}"

    return None

async def process_type1_message(message):
    """Process Type 1 message (New Trending)."""
    try:
        # First, extract all URLs from the message
        urls = []

        # Extract from plain text using the token pattern - specifically for Bubblemap URLs in parentheses
        bubblemap_matches = re.findall(token_pattern_type1, message.text)
        if bubblemap_matches:
            logger.info(f"🔍 Found token addresses from Bubblemap URLs in parentheses: {bubblemap_matches}")
            urls.extend(bubblemap_matches)

        # Handle potential duplicates
        unique_urls = []
        for url in urls:
            if url not in unique_urls:
                unique_urls.append(url)
        urls = unique_urls

        # Extract from message entities
        entity_urls = extract_urls_from_entities(message)
        for url in entity_urls:
            # Check if the URL itself is a token address
            if re.match(r'^[A-Za-z0-9]{43,44}$', url):
                urls.append(url)
            else:
                # Try to extract token from URL
                token = extract_token_from_url(url, token_pattern_type1)
                if token:
                    urls.append(token)

        # Remove duplicates while preserving order
        unique_urls = []
        for url in urls:
            if url not in unique_urls:
                unique_urls.append(url)
        urls = unique_urls

        # Log found tokens for debugging
        if urls:
            logger.info(f"🔍 Found token addresses: {urls}")

        if not urls:
            logger.info("❌ No valid token URLs found in the message")
            return

        # Process each token
        for token_address in urls:
            if token_address in processed_tokens:
                logger.info(f"⏩ Token {token_address} already processed, skipping")
                continue

            processed_tokens.add(token_address)
            logger.info(f"🔄 Processing token: {token_address}")

            # Forward token to all destination bots
            for attempt in range(MAX_RETRIES):
                try:
                    # Send to first bot (soul_scanner_bot)
                    logger.info(f"📤 Sending token to {destination_username} (attempt {attempt+1}/{MAX_RETRIES})")
                    await client.send_message(destination_username, token_address)

                    # Send to second bot (TokenTrackerbotbot_bot)
                    logger.info(f"📤 Sending token to {destination_username2} (attempt {attempt+1}/{MAX_RETRIES})")
                    await client.send_message(destination_username2, token_address)

                    # Send to third bot (WizSarumanBot)
                    logger.info(f"📤 Sending token to {destination_username3} (attempt {attempt+1}/{MAX_RETRIES})")
                    await client.send_message(destination_username3, token_address)

                    # Send to fourth bot (maestro)
                    logger.info(f"📤 Sending token to {destination_username4} (attempt {attempt+1}/{MAX_RETRIES})")
                    await client.send_message(destination_username4, token_address)

                    # Send to fifth bot (DbotxSIMBot) with /buy command
                    logger.info(f"📤 Sending token to {destination_username5} with /buy command (attempt {attempt+1}/{MAX_RETRIES})")
                    buy_command = f"/buy {token_address}"
                    await client.send_message(destination_username5, buy_command)

                    # Send to sixth bot (TrailingDbotx_bot) directly
                    logger.info(f"📤 Sending token to {destination_username6} (attempt {attempt+1}/{MAX_RETRIES})")
                    await client.send_message(destination_username6, token_address)

                    # Wait for response from first bot with reduced delay
                    response = None
                    for _ in range(2):  # Try for up to 2 seconds
                        await asyncio.sleep(1)  # Reduced to 1 second per check
                        async for msg in client.iter_messages(destination_username, limit=3):  # Increased from 5 to 10
                            # More robust check for token in message
                            if msg.text and (
                                token_address in msg.text or
                                (len(token_address) > 10 and token_address[:10] in msg.text) or
                                (len(token_address) > 20 and token_address[:20] in msg.text)
                            ):
                                response = msg
                                logger.info(f"🔍 Found response with token {token_address}")
                                break
                        if response:
                            logger.info(f"✅ Received response from {destination_username} for token {token_address}")
                            break

                    if not response:
                        logger.warning(f"⏳ No response from bot after attempt {attempt+1}, retrying...")
                        if attempt < MAX_RETRIES - 1:
                            await asyncio.sleep(RATE_LIMIT_DELAY)
                            continue
                        else:
                            logger.error(f"❌ Failed to get response after {MAX_RETRIES} attempts")
                            break

                    # Extract data from response
                    scanner_data = extract_data_from_scanner_response(response.text, token_address)

                    # Extract caller name from the message
                    caller = extract_caller_from_message(message.text)
                    if caller != "N/A":
                        logger.info(f"👤 Found caller: {caller}")
                        scanner_data["Called By"] = caller

                    # Extract number of calls from the message
                    calls = extract_calls_from_message(message.text)
                    if calls != "N/A":
                        logger.info(f"📞 Found calls: {calls}")
                        scanner_data["Calls"] = calls

                    # Extract X URL from message entities
                    x_url = "N/A"
                    if hasattr(message, 'entities') and message.entities:
                        for entity in message.entities:
                            if isinstance(entity, MessageEntityTextUrl):
                                if 'x.com' in entity.url or 'twitter.com' in entity.url:
                                    x_url = entity.url
                                    logger.info(f"🐦 Found X URL: {x_url}")
                                    break
                    scanner_data["X URL"] = x_url

                    # Extract bonding curve percentage if available
                    bonding_curve_match = re.search(r'📊\s*Bonding\s*Curve\s*Progression:\s*(\d+\.\d+%)', message.text)
                    if bonding_curve_match:
                        bonding_curve = bonding_curve_match.group(1)
                        logger.info(f"📊 Found bonding curve percentage: {bonding_curve}")
                        scanner_data["Bonding Curve"] = bonding_curve

                    # Filter tokens based on criteria
                    if not should_forward_token(scanner_data):
                        logger.info(f"⏩ Token filtered: {token_address}")
                        continue

                    # Token group search functionality has been removed

                    # Save data to Excel
                    await export_to_excel(scanner_data)

                    # Rate limiting
                    await asyncio.sleep(RATE_LIMIT_DELAY)
                    break  # Successfully processed this token, break retry loop

                except Exception as e:
                    logger.error(f"❌ Error processing token {token_address}: {e}")
                    if attempt < MAX_RETRIES - 1:
                        await asyncio.sleep(RATE_LIMIT_DELAY)
                    else:
                        logger.error(f"❌ Failed to process token after {MAX_RETRIES} attempts")

    except Exception as e:
        logger.error(f"❌ Error in process_type1_message: {e}")

async def process_type2_message(message):
    """Process Type 2 message (is up)."""
    try:
        # Extract token from URL
        urls = []
        message_text = message.text or ""

        # Extract from plain text - look for GeckoTerminal and Helius XRay URLs with flexible pattern
        # This handles both GeckoTerminal and Helius XRay formats
        for url in re.findall(r'https?://(?:www\.)?geckoterminal\.com/solana/pools/[a-zA-Z0-9]+', message_text):
            # Extract the token ID from the GeckoTerminal URL
            token_match = re.search(r'pools/([a-zA-Z0-9]+)', url)
            if token_match:
                token = token_match.group(1)
                urls.append((url, token))

        # Look for Helius XRay URLs
        for url in re.findall(r'https?://xray\.helius\.xyz/token/[A-Za-z0-9]{43,44}', message_text):
            # Extract the token ID from the Helius XRay URL
            token_match = re.search(r'token/([A-Za-z0-9]{43,44})', url)
            if token_match:
                token = token_match.group(1)
                urls.append((url, token))
                logger.info(f"🔍 Found Helius XRay URL with token: {token}")

        # Look for any token addresses directly in the message
        sol_addr_matches = re.findall(r'\b([A-Za-z0-9]{43,44})\b', message_text)
        for token in sol_addr_matches:
            if (f"direct_mention:{token}", token) not in urls:
                urls.append((f"direct_mention:{token}", token))
                logger.info(f"🔍 Found direct token address: {token}")

        # Extract from message entities with more flexible patterns
        entity_urls = extract_urls_from_entities(message)
        for url in entity_urls:
            # Try GeckoTerminal format with case-insensitive match
            token_match = re.search(r'geckoterminal\.com/solana/pools/([a-zA-Z0-9]+)', url, re.IGNORECASE)
            if token_match:
                token = token_match.group(1)
                if (url, token) not in urls:
                    urls.append((url, token))

            # Try Helius XRay format
            helius_match = re.search(r'xray\.helius\.xyz/token/([A-Za-z0-9]{43,44})', url, re.IGNORECASE)
            if helius_match:
                token = helius_match.group(1)
                if (url, token) not in urls:
                    urls.append((url, token))
                    logger.info(f"🔍 Found Helius XRay URL in entity with token: {token}")
            else:
                # Try traditional token_pattern_type2 format
                token = extract_token_from_url(url, token_pattern_type2)
                if token and (url, token) not in urls:
                    urls.append((url, token))

        # Special handling for Trailing Stop Loss messages
        if "Trailing Stop Loss Triggered" in message_text and not urls:
            # Extract token name from the message
            token_name_match = re.search(r'Trailing Stop Loss Triggered!\s+(\w+)', message_text)
            if token_name_match:
                token_name = token_name_match.group(1)
                logger.info(f"🔍 Extracted token name from Trailing Stop Loss message: {token_name}")

                # We'll need to update the Excel file with the trailing sell data for this token
                # But we need to find the token address first
                trailing_sell = extract_trailing_sell_from_message(message_text)
                if trailing_sell != "N/A":
                    logger.info(f"📉 Extracted trailing sell: {trailing_sell}")

                    # Create a special data object for updating just the trailing sell
                    data = {
                        "Token Name": token_name,
                        "Trailing Sell": trailing_sell
                    }

                    try:
                        # We'll handle this special case in the export_to_excel function
                        await export_to_excel(data)
                        logger.info(f"✅ Updated trailing sell for token: {token_name}")
                        return
                    except Exception as e:
                        logger.error(f"❌ Failed to update trailing sell for token {token_name}: {e}")
                        return
            else:
                logger.info("❌ Could not extract token name from Trailing Stop Loss message")
                return

        if not urls:
            logger.info("❌ No valid token URLs or addresses found in the message")
            return

        # Extract profit percentage with enhanced pattern matching
        profit_value = extract_profit_from_message(message_text)

        # Try additional profit patterns if the main one fails
        if not profit_value:
            # Look for patterns like "$50.6K —> $77.1K" and calculate percentage
            mc_match = re.search(r"\$(\d+\.?\d*)([KMB])?\s*—>\s*\$(\d+\.?\d*)([KMB])?", message_text)
            if mc_match:
                # Extract values and convert to float
                start_value = float(mc_match.group(1))
                start_unit = mc_match.group(2) or ''
                end_value = float(mc_match.group(3))
                end_unit = mc_match.group(4) or ''

                # Convert to same units
                multipliers = {'': 1, 'K': 1000, 'M': 1000000, 'B': 1000000000}
                start_amount = start_value * multipliers[start_unit]
                end_amount = end_value * multipliers[end_unit]

                # Calculate percentage increase or decrease
                if end_amount > start_amount:
                    # This is an increase
                    increase_ratio = end_amount / start_amount
                    # Always convert to percentage format
                    percentage = (increase_ratio - 1) * 100
                    profit_value = f"{percentage:.0f}%"
                elif end_amount < start_amount:
                    # This is a decrease
                    percentage = (1 - (end_amount / start_amount)) * 100
                    profit_value = f"-{percentage:.0f}%"  # Add negative sign for decreases

        # Still no profit value found
        if not profit_value:
            # Try a more generic approach - look for any numbers followed by % or X
            profit_matches = re.findall(r'(\d+\.?\d*)([%Xx])', message_text)
            if profit_matches:
                max_percentage = 0
                for value_str, unit in profit_matches:
                    unit = unit.upper()
                    value = float(value_str)

                    # Convert X to percentage
                    if unit == 'X':
                        percentage_value = value * 100
                        if percentage_value > max_percentage:
                            max_percentage = percentage_value
                    elif unit == '%' and value > max_percentage:
                        max_percentage = value

                # Always use percentage format
                profit_value = f"{max_percentage:.0f}%"

        if not profit_value:
            logger.info("❌ Could not extract profit value from the message")
            profit_value = "N/A"  # Use N/A instead of failing
        else:
            logger.info(f"📊 Extracted profit: {profit_value} for token(s): {[token for _, token in urls]}")

        # Extract X URL from message entities
        x_url = "N/A"
        if hasattr(message, 'entities') and message.entities:
            for entity in message.entities:
                if isinstance(entity, MessageEntityTextUrl):
                    if 'x.com' in entity.url or 'twitter.com' in entity.url:
                        x_url = entity.url
                        logger.info(f"🐦 Found X URL: {x_url}")
                        break

        # Extract bonding curve percentage if available
        bonding_curve = "N/A"
        bonding_curve_match = re.search(r'📊\s*Bonding\s*Curve\s*Progression:\s*(\d+\.\d+%)', message.text)
        if bonding_curve_match:
            bonding_curve = bonding_curve_match.group(1)
            logger.info(f"📊 Found bonding curve percentage: {bonding_curve}")

        # Extract trailing sell data if this is a Trailing Stop Loss message
        trailing_sell = extract_trailing_sell_from_message(message_text)
        if trailing_sell != "N/A":
            logger.info(f"📉 Extracted trailing sell: {trailing_sell}")

        # Update Excel with profit data
        for _, token_address in urls:
            data = {
                "Token Address": token_address,
                "Profit": profit_value,
                "Trailing Sell": trailing_sell,
                "X URL": x_url,
                "Bonding Curve": bonding_curve
            }

            try:
                await export_to_excel(data)
                logger.info(f"✅ Updated profit for token: {token_address}")
            except Exception as e:
                logger.error(f"❌ Failed to update profit for token {token_address}: {e}")

    except Exception as e:
        logger.error(f"❌ Error in process_type2_message: {e}")
        # Add full message logging for debugging
        if 'message_text' in locals():
            logger.error(f"Message content: {message_text}")

async def message_handler(event):
    """Handle all incoming messages from the source channel and bots."""
    global last_message_time, update_state

    # Update last message time
    last_message_time = time.time()

    # Save update state for persistence
    try:
        if hasattr(event, 'pts'):
            update_state['pts'] = event.pts
        if hasattr(event, 'qts'):
            update_state['qts'] = event.qts
        if hasattr(event, 'date'):
            update_state['date'] = event.date
        if hasattr(event, 'seq'):
            update_state['seq'] = event.seq

        # Periodically save the session state
        if any(update_state.values()):
            save_session_state()
    except Exception as e:
        logger.warning(f"⚠️ Could not update state: {e}")

    # Log the message arrival immediately
    message_text = event.message.text if hasattr(event, 'message') and hasattr(event.message, 'text') else "No text"

    # Determine the source of the message
    chat_username = None

    try:
        chat = await event.get_chat()
        chat_username = chat.username if hasattr(chat, 'username') else str(getattr(chat, 'id', 'unknown'))
    except Exception as e:
        # If we can't get the chat info, use a default
        logger.warning(f"⚠️ Could not get chat info: {e}")
        chat_username = "unknown"

    # Log with source information
    logger.info(f"📨 Received message from {chat_username}: {message_text[:30]}...")

    try:
        # Add to processing queue with higher priority for new messages
        await processing_queue.put((0, event))  # Lower number = higher priority
        logger.info(f"📥 Queued new message. Current queue size: {processing_queue.qsize()}")
    except Exception as e:
        logger.error(f"❌ Error queuing message: {e}")
        # Add to buffer if queue is full or other error
        message_buffer.append((0, event))
        logger.warning(f"⚠️ Added message to buffer. Buffer size: {len(message_buffer)}")

async def process_queue_worker():
    """Process messages from the queue one at a time."""
    global is_processing, message_buffer

    while True:
        try:
            # First check if we have any buffered messages to process
            if not is_processing and message_buffer:
                is_processing = True
                _, event = message_buffer.pop(0)  # Using _ to ignore the priority
                logger.info(f"📤 Processing message from buffer. Remaining buffer size: {len(message_buffer)}")

                await process_message(event)
                is_processing = False

            # Then check the queue
            elif not is_processing and not processing_queue.empty():
                is_processing = True
                _, event = await processing_queue.get()  # Using _ to ignore the priority

                await process_message(event)
                processing_queue.task_done()
                is_processing = False

            else:
                # No messages to process, sleep briefly
                await asyncio.sleep(0.1)  # Very short sleep to prevent CPU hogging

        except FloodWaitError as e:
            # Handle rate limiting
            wait_time = e.seconds
            logger.warning(f"⚠️ Rate limited! Waiting for {wait_time} seconds")
            await asyncio.sleep(wait_time)
            is_processing = False

        except (ServerError, TimedOutError) as e:
            # Handle network errors
            logger.error(f"❌ Network error: {e}. Retrying in 5 seconds...")
            await asyncio.sleep(5)
            is_processing = False

        except Exception as e:
            logger.error(f"❌ Error in queue worker: {e}")
            is_processing = False
            await asyncio.sleep(1)

async def process_message(event):
    """Process a single message."""
    try:
        # Handle both proper Message objects and string objects
        if hasattr(event, 'message'):
            # This is a proper event object
            message = event.message
            message_text = message.text if hasattr(message, 'text') else ""
        elif isinstance(event, str):
            # This is just a string, create a simple object to mimic the message structure
            logger.info(f"Processing string message instead of Message object")
            message_text = event

            # Create a simple object that mimics the necessary attributes
            class SimpleMessage:
                def __init__(self, text):
                    self.text = text
                    self.entities = []

            message = SimpleMessage(message_text)
        else:
            # Neither a proper event nor a string, log and skip
            logger.error(f"Unknown message type: {type(event)}")
            return

        logger.info(f"📩 Processing message: {message_text[:50]}...")

        # Extract all URLs and pattern matches from the message
        urls = extract_urls_from_entities(message)

        # Add text URLs that might not be in entities
        text_urls = re.findall(r'https?://\S+', message_text)
        for url in text_urls:
            if url not in urls:
                urls.append(url)

        # Enhanced pattern detection - Check patterns in the entire message
        has_type1_pattern = any(pattern in message_text for pattern in type1_pattern)
        has_type2_pattern = any(pattern in message_text.lower() for pattern in type2_pattern)

        # Extract tokens from URLs
        type1_tokens = []
        type2_tokens = []

        for url in urls:
            # Try Type 1 pattern (Bubblemap URLs in parentheses)
            token = extract_token_from_url(url, token_pattern_type1)
            if token and token not in type1_tokens:
                type1_tokens.append(token)

            # Try Type 2 pattern (GeckoTerminal URLs)
            token_match = re.search(r'geckoterminal\.com/solana/pools/([a-zA-Z0-9]+)', url)
            if token_match:
                token = token_match.group(1)
                if token and token not in type2_tokens:
                    type2_tokens.append(token)

        # Look specifically for Bubblemap URLs in parentheses
        bubblemap_matches = re.findall(r'\(https?://(?:app\.)?bubblemaps\.io/sol/token/([A-Za-z0-9]{43,44})', message_text)
        for token in bubblemap_matches:
            if token and token not in type1_tokens:
                type1_tokens.append(token)

        # Log found tokens for debugging
        if bubblemap_matches:
            logger.info(f"🔍 Found token addresses from Bubblemap URLs in parentheses: {bubblemap_matches}")

        # Get chat info to determine message source
        chat_username = None
        try:
            if hasattr(event, 'get_chat'):
                chat = await event.get_chat()
                chat_username = chat.username if hasattr(chat, 'username') else str(getattr(chat, 'id', 'unknown'))
        except Exception as e:
            logger.warning(f"⚠️ Could not get chat info: {e}")

        # Check message source
        is_from_tracker_bot = chat_username and destination_username2.replace('@', '') == chat_username
        is_from_source = chat_username and source_username.replace('@', '') == chat_username

        # Process messages based on source and content
        if is_from_tracker_bot:
            # This is from the TokenTrackerbotbot_bot - check for profit updates
            logger.info(f"🔍 Message from {destination_username2} - checking for profit updates")

            # Check for token addresses in the tracker bot messages
            if type1_tokens:
                logger.info(f"🔍 Found token addresses in tracker bot message: {type1_tokens}")

            # Process profit updates
            if has_type2_pattern or type2_tokens or "profit" in message_text.lower():
                logger.info("🔍 Type 2 message detected from tracker bot (profit update)")
                await process_type2_message(message)
            # Process Trailing Stop Loss messages
            elif "Trailing Stop Loss Triggered" in message_text:
                logger.info("🔍 Trailing Stop Loss message detected from tracker bot")
                await process_type2_message(message)
            # Also process any message with token addresses
            elif type1_tokens:
                logger.info("🔍 Token addresses found in tracker bot message")
                await process_type2_message(message)
        elif is_from_source:
            # Process messages from source channel
            if has_type1_pattern or type1_tokens:
                logger.info("🔍 Type 1 message detected (New Trending) from source channel")
                await process_type1_message(message)
            # We don't process profit matches from the source channel anymore
            # Only from TokenTrackerbotbot_bot
        else:
            # More aggressive parsing for missed messages

            # First check for any token addresses in the message
            if type1_tokens:
                if is_from_tracker_bot:
                    logger.info(f"🔍 Found token addresses in tracker bot message: {type1_tokens}")
                    await process_type2_message(message)
                elif is_from_source:
                    logger.info(f"🔍 Found token addresses in source message: {type1_tokens}")
                    await process_type1_message(message)
            # Then check for GeckoTerminal URLs in tracker bot messages
            elif is_from_tracker_bot:
                # Look for any GeckoTerminal or Helius XRay URL
                token_urls = []
                for url in urls:
                    if "geckoterminal.com" in url:
                        token_match = re.search(r'pools/([a-zA-Z0-9]+)', url)
                        if token_match:
                            token_urls.append(token_match.group(1))
                    elif "xray.helius.xyz" in url:
                        token_match = re.search(r'token/([A-Za-z0-9]{43,44})', url)
                        if token_match:
                            token_urls.append(token_match.group(1))

                if token_urls:
                    logger.info(f"🔍 Possible profit update detected from tracker bot (Token URL found: {token_urls})")
                    await process_type2_message(message)
                else:
                    logger.info("⏩ Message from tracker bot does not match any patterns, skipping")
            else:
                logger.info("⏩ Message does not match any patterns or is from the wrong source, skipping")

    except Exception as e:
        logger.error(f"❌ Error processing message: {e}")
        # Log the full message for debugging
        if 'message_text' in locals():
            logger.error(f"Message content: {message_text}")

        # Save session state on error to ensure we don't lose state
        save_session_state()

async def fetch_recent_messages():
    """Set up message catching without fetching historical messages."""
    try:
        # Instead of fetching historical messages, just get the current state
        # to ensure we don't miss any future messages
        logger.info("🔄 Setting up message catching (skipping historical messages)...")

        # Get the current update state if available
        try:
            if hasattr(client.session, 'get_update_state') and callable(client.session.get_update_state):
                state = client.session.get_update_state()
                if state:
                    pts, qts, date, seq = state
                    update_state["pts"] = pts
                    update_state["qts"] = qts
                    update_state["date"] = date
                    update_state["seq"] = seq
                    logger.info(f"✅ Current update state: pts={pts}, qts={qts}, date={date}, seq={seq}")
        except Exception as e:
            logger.warning(f"⚠️ Could not get update state: {e}")

        # Save the session state
        save_session_state()
        logger.info("✅ Ready to receive new messages")

    except Exception as e:
        logger.error(f"❌ Error in fetch_recent_messages: {e}")
        # Continue anyway - we'll just start receiving messages from now

async def check_for_missed_messages():
    """Periodic check for missed messages if we haven't received any recently."""
    global last_message_time

    while True:
        try:
            # Wait for the check interval
            await asyncio.sleep(CONNECTION_CHECK_INTERVAL * 60)  # Convert minutes to seconds

            # If no messages received recently, fetch recent messages again
            current_time = time.time()
            if current_time - last_message_time > MISSED_MESSAGE_THRESHOLD:
                logger.warning(f"⚠️ No messages received in the last {MISSED_MESSAGE_THRESHOLD/60:.1f} minutes! Fetching recent messages...")
                await fetch_recent_messages()
                last_message_time = current_time  # Reset timer after fetching
        except Exception as e:
            logger.error(f"❌ Error in missed message checker: {e}")
            await asyncio.sleep(60)  # Wait a bit after error and try again

async def main():
    """Main entry point for the application."""
    global client, last_message_time

    # Initialize the last_message_time to the current time
    last_message_time = time.time()

    # Print startup information
    print("\n" + "="*50)
    print("Solana Token Tracker Bot")
    print("="*50)
    print(f"Source Channel: {source_username}")
    print(f"Destination Bot 1: {destination_username}")
    print(f"Destination Bot 2: {destination_username2}")
    print(f"Destination Bot 3: {destination_username3}")
    print(f"Destination Bot 4: {destination_username4}")
    print(f"Destination Bot 5: {destination_username5} (with /buy command)")
    print(f"Destination Bot 6: {destination_username6}")
    print(f"Excel File: {EXCEL_FILE}")
    print(f"Using Morocco timezone: {MOROCCO_TZ}")
    print("="*50 + "\n")

    # Initialize Excel file
    initialize_excel()

    # Load session state if available
    load_session_state()

    # Create and start the Telegram client
    logger.info("🚀 Starting Telegram client...")

    # Try to use a saved session string if available
    session = None
    if os.path.exists(SESSION_STRING_FILE):
        try:
            with open(SESSION_STRING_FILE, 'r') as f:
                session_string = f.read().strip()
                if session_string:
                    session = StringSession(session_string)
                    logger.info("✅ Loaded session string from file")
        except Exception as e:
            logger.warning(f"⚠️ Could not load session string: {e}")

    # Create the client with the session if available
    if session:
        client = TelegramClient(session, api_id, api_hash)
    else:
        client = TelegramClient("solbix_data", api_id, api_hash)

    try:
        await client.start(phone_number)
        logger.info("✅ Client started successfully")

        # Save the session string
        if isinstance(client.session, StringSession):
            with open(SESSION_STRING_FILE, 'w') as f:
                f.write(client.session.save())
            logger.info("✅ Saved session string to file")

        # First fetch recent messages to catch up on any we might have missed
        await fetch_recent_messages()

        # Set up event handler for source channel
        client.add_event_handler(
            message_handler,
            events.NewMessage(chats=[source_username])
        )

        # Set up edited message handler (also important to catch updates)
        client.add_event_handler(
            message_handler,
            events.MessageEdited(chats=[source_username])
        )

        # Set up event handler for the second bot (TokenTrackerbotbot_bot)
        client.add_event_handler(
            message_handler,
            events.NewMessage(chats=[destination_username2])
        )

        # Set up edited message handler for the second bot
        client.add_event_handler(
            message_handler,
            events.MessageEdited(chats=[destination_username2])
        )

        # Start the queue worker
        logger.info("🔄 Starting queue worker...")
        asyncio.create_task(process_queue_worker())

        # Start the missed message checker
        logger.info("🔄 Starting missed message checker...")
        asyncio.create_task(check_for_missed_messages())

        logger.info(f"🎯 Bot is now monitoring {source_username} and {destination_username2} for new and edited messages")

        # Keep the bot running with periodic reconnection checks and state saving
        last_save_time = time.time()
        save_interval = 300  # Save state every 5 minutes

        while True:
            current_time = time.time()

            # Check connection
            if not client.is_connected():
                logger.warning("⚠️ Client disconnected. Attempting to reconnect...")
                await client.connect()

                # Fetch messages after reconnection
                logger.info("🔄 Reconnected, fetching recent messages...")
                await fetch_recent_messages()

            # Periodically save session state
            if current_time - last_save_time > save_interval:
                logger.info("💾 Saving session state...")
                save_session_state()
                last_save_time = current_time

            # Process any buffered messages if the queue is empty
            if not processing_queue.empty() and message_buffer:
                logger.info(f"📤 Moving {len(message_buffer)} buffered messages to queue")
                for buffered_message in message_buffer[:]:
                    try:
                        await processing_queue.put(buffered_message)
                        message_buffer.remove(buffered_message)
                    except Exception as e:
                        logger.error(f"❌ Error moving buffered message to queue: {e}")

            await asyncio.sleep(KEEPALIVE_INTERVAL)  # Check connection more frequently

    except Exception as e:
        logger.error(f"❌ Error in main: {e}")
    finally:
        # Save session state before disconnecting
        try:
            logger.info("💾 Saving final session state...")
            save_session_state()
        except Exception as e:
            logger.error(f"❌ Error saving final session state: {e}")

        if client.is_connected():
            await client.disconnect()
        logger.info("👋 Bot has been stopped")

if __name__ == "__main__":
    # Run the bot
    asyncio.run(main())