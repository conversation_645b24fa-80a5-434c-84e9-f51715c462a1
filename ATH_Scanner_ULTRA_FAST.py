from telethon import Telegram<PERSON>lient, events
import asyncio
import re
import logging
import os
import time
from datetime import datetime
import pytz
from openpyxl import Workbook, load_workbook
from openpyxl.styles import Pattern<PERSON>ill
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# ATH Scanner specific credentials
API_ID = os.getenv("ATH_API_ID")
API_HASH = os.getenv("ATH_API_HASH")
PHONE = os.getenv("ATH_PHONE")
DESTINATION_BOT = os.getenv("SOUL_SCANNER_BOT", "@soul_scanner_bot")

# ATH-specific configuration
SOURCE_USERNAME = "@ATH_Solana_Alerts"
SESSION_FILE = "ATH/ATH_Scanner_ULTRA_FAST.session"
EXCEL_FILE = "ATH_Scanner_ULTRA_FAST_data.xlsx"
WHITELIST = ["ATH"]
BLACKLIST = ["SellAmount"]

# ULTRA-FAST SETTINGS
WORKER_COUNT = 5
MAX_QUEUE_SIZE = 1000

# Global variables
client = None
processed_tokens = set()
message_queue = asyncio.Queue(maxsize=MAX_QUEUE_SIZE)

# Morocco timezone
MOROCCO_TZ = pytz.timezone('Africa/Casablanca')

# Logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s', handlers=[logging.StreamHandler()])
logger = logging.getLogger(__name__)

# Excel headers
HEADERS = ["Timestamp", "Token Address", "Token Name", "Source Age", "MC", "Age", "Scans", "Snipers"]

def initialize_excel():
    if not os.path.exists(EXCEL_FILE):
        wb = Workbook()
        ws = wb.active
        ws.append(HEADERS)
        wb.save(EXCEL_FILE)
        logger.info(f"✅ Created ULTRA-FAST ATH Excel: {EXCEL_FILE}")

def extract_token_from_text(message_text):
    """ULTRA-FAST token extraction for ATH messages"""
    # ATH tokens ending with "pump"
    token_pattern = r"([A-Za-z0-9]{39,40}pump)"
    token_matches = re.findall(token_pattern, message_text)
    if token_matches:
        return token_matches[0]
    return None

def extract_source_age(message_text):
    """ULTRA-FAST source age extraction"""
    age_patterns = [
        r"(\d+\.?\d*)\s*(?:min|minute|minutes|m)\s*ago",
        r"(\d+\.?\d*)\s*(?:hour|hours|h)\s*ago",
        r"(\d+\.?\d*)\s*(?:day|days|d)\s*ago"
    ]
    
    for pattern in age_patterns:
        match = re.search(pattern, message_text, re.IGNORECASE)
        if match:
            return f"{match.group(1)} ago"
    return "N/A"

async def export_to_excel(data):
    """ULTRA-FAST Excel export"""
    try:
        wb = load_workbook(EXCEL_FILE)
        ws = wb.active
        
        timestamp = datetime.now(MOROCCO_TZ).strftime("%Y-%m-%d %H:%M:%S")
        
        ws.append([
            timestamp,
            data.get("Token Address", "N/A"),
            data.get("Token Name", "N/A"),
            data.get("Source Age", "N/A"),
            data.get("MC", "N/A"),
            data.get("Age", "N/A"),
            data.get("Scans", "N/A"),
            data.get("Snipers", "N/A")
        ])
        
        wb.save(EXCEL_FILE)
        logger.info(f"⚡ ATH Excel saved: {data.get('Token Address', 'N/A')[:10]}...")
        
    except Exception as e:
        logger.error(f"❌ ATH Excel error: {e}")

async def message_handler(event):
    """ULTRA-FAST ATH message handler"""
    message = event.message
    message_text = message.message

    if not message_text:
        return

    # INSTANT ATH check
    if not any(pattern.lower() in message_text.lower() for pattern in WHITELIST):
        return

    # INSTANT blacklist check
    if any(word.lower() in message_text.lower() for word in BLACKLIST):
        return

    # INSTANT queuing
    try:
        await message_queue.put((time.time(), message))
        logger.info(f"⚡ ATH QUEUED: {message_text[:30]}... | Queue: {message_queue.qsize()}")
    except asyncio.QueueFull:
        logger.warning("🚨 ATH Queue full!")

async def ultra_fast_ath_worker(worker_id):
    """ULTRA-FAST ATH worker"""
    logger.info(f"🚀 ATH-WORKER-{worker_id}: Started")
    
    while True:
        try:
            timestamp, message = await message_queue.get()
            queue_time = time.time() - timestamp
            
            logger.info(f"⚡ ATH-WORKER-{worker_id}: Processing (queued {queue_time:.2f}s)")
            
            await process_ath_message_ultra_fast(message, worker_id)
            message_queue.task_done()
            
        except Exception as e:
            logger.error(f"❌ ATH-WORKER-{worker_id}: {e}")
            await asyncio.sleep(0.01)

async def process_ath_message_ultra_fast(message, worker_id):
    """ULTRA-FAST ATH message processing"""
    message_text = message.message
    
    # INSTANT token extraction
    token_address = extract_token_from_text(message_text)
    if not token_address:
        return

    # SPEED CHECK
    if token_address in processed_tokens:
        return

    # Mark processed IMMEDIATELY
    processed_tokens.add(token_address)
    
    # Extract source age instantly
    source_age = extract_source_age(message_text)
    
    logger.info(f"🎯 ATH-WORKER-{worker_id}: NEW ATH TOKEN: {token_address} | Age: {source_age}")

    # INSTANT bot communication
    try:
        await client.send_message(DESTINATION_BOT, token_address)
        logger.info(f"⚡ ATH-WORKER-{worker_id}: Sent to bot INSTANTLY")
        
        # Start response monitoring
        asyncio.create_task(ultra_fast_ath_response_monitor(token_address, source_age, worker_id))
        
    except Exception as e:
        logger.error(f"❌ ATH-WORKER-{worker_id}: Send error: {e}")

async def ultra_fast_ath_response_monitor(token_address, source_age, worker_id):
    """ULTRA-FAST ATH response monitoring"""
    for attempt in range(15):  # 4.5 seconds total
        await asyncio.sleep(0.3)  # Check every 0.3 seconds
        
        try:
            messages = await client.get_messages(DESTINATION_BOT, limit=20)
            
            for msg in messages:
                if msg.text and (
                    token_address in msg.text or
                    (len(token_address) > 8 and token_address[:8] in msg.text) or
                    any(indicator in msg.text for indicator in ["**$", "MC:", "Age:", "Scans:"])
                ):
                    response_time = (attempt + 1) * 0.3
                    logger.info(f"🔍 ATH-WORKER-{worker_id}: RESPONSE in {response_time:.1f}s!")
                    
                    # INSTANT data extraction
                    data = {
                        "Token Address": token_address,
                        "Source Age": source_age,
                        "Token Name": "N/A",
                        "MC": "N/A",
                        "Age": "N/A",
                        "Scans": "N/A",
                        "Snipers": "N/A"
                    }
                    
                    # Quick data extraction
                    name_match = re.search(r"\*\*\$([A-Za-z0-9]+)\*\*", msg.text)
                    if name_match:
                        data["Token Name"] = name_match.group(1)
                    
                    mc_match = re.search(r"MC:\s*\$?([0-9,.]+[KMB]?)", msg.text)
                    if mc_match:
                        data["MC"] = mc_match.group(1)
                    
                    age_match = re.search(r"Age:\s*([^🕒\n]+)", msg.text)
                    if age_match:
                        data["Age"] = age_match.group(1).strip()
                    
                    scans_match = re.search(r"Scans:\s*(\d+)", msg.text)
                    if scans_match:
                        data["Scans"] = scans_match.group(1)
                    
                    snipers_match = re.search(r"Snipers:\s*(\d+)", msg.text)
                    if snipers_match:
                        data["Snipers"] = snipers_match.group(1)
                    
                    # INSTANT Excel save
                    await export_to_excel(data)
                    return
                    
        except Exception as e:
            logger.error(f"❌ ATH-WORKER-{worker_id}: Monitor error: {e}")
    
    # Save minimal data
    minimal_data = {
        "Token Address": token_address,
        "Source Age": source_age,
        "Token Name": "N/A",
        "MC": "N/A",
        "Age": "N/A",
        "Scans": "N/A",
        "Snipers": "N/A"
    }
    await export_to_excel(minimal_data)
    logger.info(f"⚠️ ATH-WORKER-{worker_id}: Saved minimal data")

async def main():
    """ULTRA-FAST ATH main function"""
    global client
    
    logger.info("🚀 Starting ULTRA-FAST ATH Scanner")
    logger.info(f"📍 Source: {SOURCE_USERNAME}")
    logger.info(f"📍 Workers: {WORKER_COUNT}")
    logger.info(f"📊 Excel: {EXCEL_FILE}")
    logger.info("=" * 50)

    initialize_excel()
    client = TelegramClient(SESSION_FILE.replace('.session', ''), API_ID, API_HASH)

    try:
        await client.start(phone=PHONE)
        logger.info("✅ ATH Client started ULTRA-FAST")

        # Resolve source
        source_entity = await client.get_entity(SOURCE_USERNAME)
        logger.info(f"✅ ATH Source resolved: {source_entity.title}")

        # Start workers
        workers = []
        for i in range(WORKER_COUNT):
            worker = asyncio.create_task(ultra_fast_ath_worker(i + 1))
            workers.append(worker)
        
        logger.info(f"🚀 Started {WORKER_COUNT} ULTRA-FAST ATH workers")

        # Register event handler
        client.add_event_handler(message_handler, events.NewMessage(chats=[source_entity]))
        logger.info("⚡ ATH LISTENING: ULTRA-FAST mode activated!")

        # Keep running
        await client.run_until_disconnected()

    except Exception as e:
        logger.error(f"❌ ATH Error: {e}")
    finally:
        if client:
            await client.disconnect()

if __name__ == "__main__":
    asyncio.run(main())
