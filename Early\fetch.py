from telethon import TelegramClient

# Configuration
API_ID = "28906328"  # Replace with your Telegram API ID
API_HASH = "dad67f49a710f302f126351fe407a2f6"  # Replace with your Telegram API HASH
SESSION_NAME = "earlytrading_data"  # Session name for Telethon
USERNAME = "WizGandalfBot"  # Replace with the username you want to fetch the last message from

# Initialize Telegram client
client = TelegramClient(SESSION_NAME, API_ID, API_HASH)

async def get_last_message():
    try:
        # Fetch the last message from the user
        async for message in client.iter_messages(USERNAME, limit=1):
            print(f"Last message text: {message.text}")

            # Check if the message contains buttons
            if message.buttons:
                print("Buttons found:")
                for row in message.buttons:
                    for button in row:
                        print(f"Button Text: {button.text}, Button Data: {button.data}")
            else:
                print("No buttons found in the last message.")
    except Exception as e:
        print(f"Error fetching message: {e}")

# Run the script
async def main():
    await client.start()
    print("Telethon client started. Fetching the last message...")
    await get_last_message()

if __name__ == "__main__":
    with client:
        client.loop.run_until_complete(main())