from telethon import TelegramClient, events
import asyncio
import re
import logging
import os
from datetime import datetime
import pytz
from openpyxl import Workbook, load_workbook
from openpyxl.styles import Pattern<PERSON>ill
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# ATH Scanner specific credentials
API_ID = os.getenv("ATH_API_ID")
API_HASH = os.getenv("ATH_API_HASH")
PHONE = os.getenv("ATH_PHONE")
DESTINATION_BOT = os.getenv("SOUL_SCANNER_BOT", "@soul_scanner_bot")

# ATH-specific configuration
SOURCE_USERNAME = "@gmgnsignals"
SESSION_FILE = "ATH/ATH_Scanner.session"  # Session inside folder
EXCEL_FILE = "ATH_Scanner_data.xlsx"      # Excel in main directory
WHITELIST = ["ATH Price"]
BLACKLIST = ["SellAmount"]

# Script settings
RATE_LIMIT_DELAY = 3
WAIT_TIME = 5

# 🚀 ULTRA-FAST Global variables
client = None
processed_tokens = set()
processing_lock = asyncio.Lock()  # Keep for Excel safety
message_queue = asyncio.Queue(maxsize=500)  # GENIUS: High-speed queue
WORKER_COUNT = 3  # ULTRA-FAST: Multiple parallel workers
stats = {"processed": 0, "queued": 0, "responses": 0}
import time  # For performance timing

HEADERS = [
    # Your specified order first
    "Timestamp", "Token Address", "Token Name", "Warnings", "Age", "Source Age", "MC", "T-MC",
    "Liq", "Liq SOL", "First 20 Percentage", "First 20", "Whale Fish Pattern",
    "Made", "Volume", "Price", "Scans", "Hodls", "Top Holders", "Snipers",
    "Snipers Percentage",
    # Remaining columns in any order
    "Dex Paid", "High", "Dev", "LP Burnt", "Bundled", "Airdrop", "Burnt",
    "Sold", "Bond"
]

# Morocco timezone
MOROCCO_TZ = pytz.timezone('Africa/Casablanca')

# Logging configuration (console only)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def initialize_excel():
    """Initialize Excel file with headers if it doesn't exist"""
    if not os.path.exists(EXCEL_FILE):
        wb = Workbook()
        ws = wb.active
        ws.append(HEADERS)
        wb.save(EXCEL_FILE)
        logger.info(f"✅ Created new Excel file: {EXCEL_FILE}")
    else:
        logger.info(f"📊 Using existing Excel file: {EXCEL_FILE}")

def extract_token_from_text(message_text):
    """Extract token from plain text (last 4 characters must be 'pump')."""
    token_pattern = r"([A-Za-z0-9]{39,40}pump)"
    token_matches = re.findall(token_pattern, message_text)
    if token_matches:
        return token_matches[0]
    return None

def extract_source_age(message_text):
    """Extract source age from plain text."""
    source_age_pattern = r'Open:\s([^*]+)\sago'
    source_age_match = re.search(source_age_pattern, message_text, re.IGNORECASE)
    if source_age_match:
        return source_age_match.group(1).strip()
    return "N/A"

def extract_data_from_scanner_response(message_text, token_address, source_age):
    """Extract data from scanner bot response"""
    data = {
        "Token Address": token_address,
        "Token Name": "N/A",
        "Warnings": "N/A",
        "Age": "N/A",
        "Source Age": source_age,
        "MC": "N/A",
        "T-MC": "N/A",
        "Liq": "N/A",
        "Liq SOL": "N/A",
        "Volume": "N/A",
        "Price": "N/A",
        "Dex Paid": "N/A",
        "Scans": "N/A",
        "Hodls": "N/A",
        "High": "N/A",
        "Snipers": "N/A",
        "Snipers Percentage": "N/A",
        "Dev": "N/A",
        "Top Holders": "N/A",
        "LP Burnt": "N/A",
        "Bundled": "N/A",
        "Airdrop": "N/A",
        "Burnt": "N/A",
        "Sold": "N/A",
        "Made": "N/A",
        "Bond": "N/A",
        "First 20 Percentage": "N/A",
        "First 20": "N/A",
        "Whale Fish Pattern": "N/A"
    }

    # Token Name
    token_name_match = re.search(r"\*\*\$([A-Za-z0-9]+)\*\*", message_text)
    if token_name_match:
        data["Token Name"] = token_name_match.group(1)

    # Warnings extraction
    warnings = re.findall(r"(🚨|⚠️)?\s*__([^__]+)__", message_text)
    if warnings:
        filtered_warnings = [
            f"{icon} {warning}" if icon else warning
            for icon, warning in warnings
            if any(keyword.lower() in warning.lower()
                 for keyword in ["High", "Migrating", "Low", "Dev", "Sniped", "Abnormal"])
        ]
        if filtered_warnings:
            data["Warnings"] = " | ".join(filtered_warnings)

    # Age
    age_match = re.search(r"🕒 \*\*Age:\*\* ([^\n]+)", message_text)
    if age_match:
        data["Age"] = age_match.group(1).strip()

    # MC (Market Cap)
    mc_match = re.search(r"💰 \*\*MC:\*\* ([^\s•]+)", message_text)
    if mc_match:
        data["MC"] = mc_match.group(1).strip()

    # T-MC (Target Market Cap)
    t_mc_match = re.search(r"🔝 __([^_]+)__", message_text)
    if t_mc_match:
        data["T-MC"] = t_mc_match.group(1).strip()

    # Liquidity
    liq_match = re.search(r"💧 \*\*Liq:\*\* ([^\s•]+)", message_text)
    if liq_match:
        data["Liq"] = liq_match.group(1).strip()

    # Liquidity SOL
    liq_sol_match = re.search(r"💧.*?(\d+\.?\d* SOL)", message_text)
    if liq_sol_match:
        data["Liq SOL"] = liq_sol_match.group(1)

    # Volume - Updated pattern for: 📈 **Vol:** __1h__: $527.9K | __1d__: $1.4M
    volume_match = re.search(r"📈 \*\*Vol:\*\* ([^📈\n]+)", message_text)
    if volume_match:
        data["Volume"] = volume_match.group(1).strip()

    # Price
    price_match = re.search(r"📈 \*\*Price:\*\* ([^*\n]+)", message_text)
    if price_match:
        data["Price"] = price_match.group(1).strip()

    # Dex Paid
    if "✅" in message_text and "Dex" in message_text:
        data["Dex Paid"] = "✅"
    elif "❌" in message_text and "Dex" in message_text:
        data["Dex Paid"] = "❌"

    # Scans - Updated pattern for: ⚡ **Scans:** 627 | 🔗 [X](https://x.com/...)
    scans_match = re.search(r"⚡ \*\*Scans:\*\* (\d+)", message_text)
    if scans_match:
        data["Scans"] = scans_match.group(1)

    # Hodls - Updated pattern for: **👥 **[**Hodls**](...): 1,438 • Top: 15.5%
    hodls_match = re.search(r"\*\*👥 \*\*\[\*\*Hodls\*\*\]\([^)]+\): ([\d,]+)", message_text)
    if hodls_match:
        data["Hodls"] = hodls_match.group(1)

    # High - Multiple patterns for: ┗ High: [5.2%](link) or ┗ High: 5.2% or High: 5.2%
    high_match = re.search(r"┗ High: \[(\d+\.?\d*%)\]", message_text)  # [5.2%](link) format
    if not high_match:
        high_match = re.search(r"┗ High: (\d+\.?\d*%)", message_text)  # 5.2% format
    if not high_match:
        # Alternative pattern for other High formats
        high_match = re.search(r"High: (\d+\.?\d*%)", message_text)

    if high_match:
        data["High"] = high_match.group(1)

    # Snipers
    snipers_match = re.search(r"🎯 \*\*Snipers:\*\* (\d+)", message_text)
    if snipers_match:
        data["Snipers"] = snipers_match.group(1)

    # Snipers Percentage
    snipers_percentage_match = re.search(r"🎯.*?(\d+\.?\d*%)", message_text)
    if snipers_percentage_match:
        data["Snipers Percentage"] = snipers_percentage_match.group(1)

    # Dev
    dev_match = re.search(r"🛠️ Dev.*?(\d+ SOL)", message_text)
    if dev_match:
        data["Dev"] = dev_match.group(1)

    # Top Holders - Updated pattern for: **👥 **[**Hodls**](...): 1,438 • Top: 15.5%
    top_holders_match = re.search(r"\*\*👥 \*\*\[\*\*Hodls\*\*\]\([^)]+\): [\d,]+ • Top: (\d+\.?\d*%)", message_text)
    if top_holders_match:
        data["Top Holders"] = top_holders_match.group(1)

    # LP Burnt
    lp_match = re.search(r"\*\*:\*\* (\d+%) Burnt", message_text)
    if lp_match:
        data["LP Burnt"] = lp_match.group(1)

    # Sold
    sold_match = re.search(r"\| Sold:\s*(\d+\.?\d*%)", message_text)
    if sold_match:
        data["Sold"] = sold_match.group(1)

    # Made patterns
    made_match = re.search(r"Made: (\d+)", message_text)
    if made_match:
        data["Made"] = made_match.group(1)

    # Bond patterns
    bond_match = re.search(r"Bond: (\d+)", message_text)
    if bond_match:
        data["Bond"] = bond_match.group(1)

    # Bundled patterns
    bundled_match = re.search(r"Bundled: (\d+%)", message_text)
    if bundled_match:
        data["Bundled"] = bundled_match.group(1)

    # Airdrop patterns
    airdrop_match = re.search(r"Airdrop: (\d+%)", message_text)
    if airdrop_match:
        data["Airdrop"] = airdrop_match.group(1)

    # Burnt patterns (different from LP Burnt)
    burnt_match = re.search(r"Burnt: ([^🔥\n]+)", message_text)
    if burnt_match:
        data["Burnt"] = burnt_match.group(1).strip()

    # First 20 detailed patterns - extract from the summary line
    # Pattern: [🎯 First 20](link): 26% | 11 🐟 • 19%
    first20_detailed = re.search(r"\*\*:\*\* (\d+%) \| (\d+) ([🛠🐟🍤🐳🌱]) • (\d+%)", message_text)
    if first20_detailed:
        data["First 20 Percentage"] = first20_detailed.group(1)
        count = first20_detailed.group(2)
        emoji = first20_detailed.group(3)
        percentage = first20_detailed.group(4)
        data["First 20"] = f"{count} {emoji} • {percentage}"

    # Extract whale/fish patterns and analyze them
    whale_fish_patterns = extract_whale_fish_patterns(message_text)
    if whale_fish_patterns:
        data.update(whale_fish_patterns)

    return data

def extract_whale_fish_patterns(text):
    """Extract whale/fish emoji patterns"""
    patterns = {}
    
    # Extract the detailed holder pattern from the lines with solscan links
    # Look for pattern like: [🛠](https://solscan.io/account/...)[🐟](https://solscan.io/account/...)
    holder_pattern_match = re.findall(r'\[([🛠🐟🍤🐳🌱])\]\(https://solscan\.io/account/[^)]+\)', text)
    
    if holder_pattern_match:
        # Combine all holder emojis to create the full pattern
        all_patterns = ''.join(holder_pattern_match)
        patterns['Whale Fish Pattern'] = all_patterns
    
    return patterns

def parse_market_cap_value(mc_text):
    """Parse market cap text and return numeric value in thousands"""
    if not mc_text or mc_text == "N/A":
        return 0

    # Remove $ and convert to uppercase
    mc_clean = mc_text.replace("$", "").replace(",", "").upper()

    try:
        if "K" in mc_clean:
            # Convert K to thousands
            value = float(mc_clean.replace("K", ""))
            return value
        elif "M" in mc_clean:
            # Convert M to thousands (1M = 1000K)
            value = float(mc_clean.replace("M", ""))
            return value * 1000
        elif "B" in mc_clean:
            # Convert B to thousands (1B = 1,000,000K)
            value = float(mc_clean.replace("B", ""))
            return value * 1000000
        else:
            # Assume it's already in dollars, convert to thousands
            value = float(mc_clean)
            return value / 1000
    except (ValueError, AttributeError):
        return 0

def get_tmc_color(tmc_value_k):
    """Get color fill for T-MC based on value in thousands"""
    if 30 <= tmc_value_k <= 60:
        # Yellow
        return PatternFill(start_color="FFFF00", end_color="FFFF00", fill_type="solid")
    elif 61 <= tmc_value_k <= 100:
        # Light Green
        return PatternFill(start_color="90EE90", end_color="90EE90", fill_type="solid")
    elif tmc_value_k >= 101:
        # Light Blue
        return PatternFill(start_color="ADD8E6", end_color="ADD8E6", fill_type="solid")
    else:
        # No color for values below $30K
        return None

async def export_to_excel(data):
    """Export data to Excel with thread-safety."""
    async with processing_lock:
        try:
            wb = load_workbook(EXCEL_FILE)
            ws = wb.active

            # Check if token already exists
            token_exists = False
            for row_idx, row in enumerate(ws.iter_rows(min_row=2, values_only=True), start=2):
                if row[1] == data.get("Token Address"):  # Token Address is in column 2
                    token_exists = True
                    logger.info(f"⏩ Token {data['Token Address']} already exists, skipping")
                    return

            if not token_exists:
                # Use Morocco time for timestamp
                morocco_time = datetime.now(MOROCCO_TZ).strftime("%Y-%m-%d %H:%M:%S")
                row = [
                    # Your specified order first
                    morocco_time,
                    data["Token Address"],
                    data["Token Name"],
                    data["Warnings"],
                    data["Age"],
                    data["Source Age"],
                    data["MC"],
                    data["T-MC"],
                    data["Liq"],
                    data["Liq SOL"],
                    data["First 20 Percentage"],
                    data["First 20"],
                    data["Whale Fish Pattern"],
                    data["Made"],
                    data["Volume"],
                    data["Price"],
                    data["Scans"],
                    data["Hodls"],
                    data["Top Holders"],
                    data["Snipers"],
                    data["Snipers Percentage"],
                    # Remaining columns in any order
                    data["Dex Paid"],
                    data["High"],
                    data["Dev"],
                    data["LP Burnt"],
                    data["Bundled"],
                    data["Airdrop"],
                    data["Burnt"],
                    data["Sold"],
                    data["Bond"]
                ]
                ws.append(row)

                # Apply T-MC column coloring
                current_row = ws.max_row
                tmc_column_index = 8  # T-MC is the 8th column (index 7, but Excel is 1-based)
                tmc_cell = ws.cell(row=current_row, column=tmc_column_index)

                # Parse T-MC value and apply color
                tmc_value_k = parse_market_cap_value(data["T-MC"])
                tmc_color = get_tmc_color(tmc_value_k)

                if tmc_color:
                    tmc_cell.fill = tmc_color
                    logger.info(f"🎨 Applied color to T-MC: {data['T-MC']} (${tmc_value_k}K)")

                logger.info(f"✅ Added new row for token {data['Token Address']}")

            wb.save(EXCEL_FILE)
            logger.info(f"✅ Data saved to {EXCEL_FILE}")
        except Exception as e:
            logger.error(f"❌ Failed to export data to Excel: {e}")

async def forward_token(token_address):
    """Forward the token address to the destination."""
    try:
        await client.send_message(DESTINATION_BOT, token_address)
        logger.info(f"✅ Forwarded token to destination: {token_address}")
    except Exception as e:
        logger.error(f"❌ Failed to forward token: {e}")

async def process_one_message():
    """Fetch and process one message at a time."""
    global last_processed_message_id

    async with processing_lock:
        # Fetch message from source
        messages = await client.get_messages(SOURCE_USERNAME, limit=1)
        if not messages:
            logger.info("⚠️ No new messages found.")
            return

        message = messages[0]

        # Skip if already processed
        if last_processed_message_id and message.id <= last_processed_message_id:
            logger.info("⚠️ No new messages to process.")
            return

        last_processed_message_id = message.id
        message_text = message.message

        # Skip non-text messages
        if message_text is None:
            logger.info("⚠️ Message contains no text. Skipping...")
            return

        logger.info(f"📩 New message from {SOURCE_USERNAME}: {message_text}")

        # Extract Token Address
        token_address = extract_token_from_text(message_text)

        # Extract Source Age
        source_age = extract_source_age(message_text)
        logger.info(f"✅ Extracted Source Age: {source_age}")

        # Validation checks
        if not token_address:
            logger.info("⚠️ No token found in the message.")
            return

        if token_address in processed_tokens:
            logger.info(f"⚠️ Token already processed: {token_address}")
            return

        # Whitelist/Blacklist checks
        message_text_lower = message_text.lower()
        if WHITELIST and not any(word.lower() in message_text_lower for word in WHITELIST):
            logger.info("⚠️ Message does not contain whitelist keywords.")
            return
        if BLACKLIST and any(word.lower() in message_text_lower for word in BLACKLIST):
            logger.info("⚠️ Message contains blacklist keywords.")
            return

        # Forward token to destination bot
        processed_tokens.add(token_address)
        logger.info(f"🎯 Found token: {token_address} | Source Age: {source_age}")

        await forward_token(token_address)
        logger.info(f"⏳ Waiting for {WAIT_TIME} seconds...")
        await asyncio.sleep(WAIT_TIME)

        # Try to get response from destination bot with better matching
        response = None
        for attempt in range(3):  # Try 3 times
            dest_messages = await client.get_messages(DESTINATION_BOT, limit=10)  # Get more messages
            if dest_messages:
                # Look for a message that contains our token or scanner data
                for msg in dest_messages:
                    if msg.text and (
                        token_address in msg.text or
                        (len(token_address) > 10 and token_address[:10] in msg.text) or
                        any(indicator in msg.text for indicator in ["**$", "MC:", "Age:", "Scans:", "💰", "🕒"])
                    ):
                        response = msg
                        logger.info(f"🔍 Found scanner response for token {token_address}")
                        break

                if response:
                    break
                else:
                    logger.info(f"⏳ No matching response found, waiting 2 more seconds... (attempt {attempt + 1}/3)")
                    await asyncio.sleep(2)
            else:
                logger.info(f"⏳ No messages from bot, waiting 2 more seconds... (attempt {attempt + 1}/3)")
                await asyncio.sleep(2)

        if response:
            logger.info(f"📩 Destination bot response: {response.text[:200]}...")
            combined_data = extract_data_from_scanner_response(response.text, token_address, source_age)
            await export_to_excel(combined_data)
            logger.info("✅ Data extracted and saved to Excel")
        else:
            logger.warning("⚠️ No valid response from destination bot after 3 attempts")
            # Still save the token with minimal data
            minimal_data = {
                "Token Address": token_address,
                "Token Name": "N/A",
                "Warnings": "N/A",
                "Age": "N/A",
                "Source Age": source_age
            }
            await export_to_excel(minimal_data)
            logger.info("✅ Saved minimal token data to Excel")

        logger.info("🛑 Finished processing message.")

async def message_handler(event):
    """🚀 ULTRA-FAST message handler - NO LOCKS, NO DELAYS"""
    # REMOVED processing_lock for MAXIMUM SPEED - Allow parallel processing
    # REMOVED delay for INSTANT processing

    message = event.message
    message_text = message.message

    # INSTANT skip check
    if not message_text:
        return

    # INSTANT filtering
    message_text_lower = message_text.lower()
    if WHITELIST and not any(word.lower() in message_text_lower for word in WHITELIST):
        return
    if BLACKLIST and any(word.lower() in message_text_lower for word in BLACKLIST):
        return

    logger.info(f"⚡ ULTRA-FAST: Processing {message_text[:50]}...")

    # INSTANT token extraction
    token_address = extract_token_from_text(message_text)
    if not token_address:
        return

    # SPEED CHECK: Skip duplicates instantly
    if token_address in processed_tokens:
        logger.info(f"⚡ Already processed: {token_address[:10]}...")
        return

    # Extract source age instantly
    source_age = extract_source_age(message_text)

    # Mark processed IMMEDIATELY
    processed_tokens.add(token_address)
    logger.info(f"🎯 ULTRA-FAST: NEW TOKEN: {token_address} | Age: {source_age}")

    # INSTANT bot communication
    await forward_token(token_address)
    logger.info(f"📤 Token sent to bot, waiting 5 seconds for complete processing...")

    # 🎯 HYBRID: Wait 5 seconds for bot to process completely, then check for response
    await asyncio.sleep(5)  # Give bot proper time to process and respond with complete data

    # Enhanced response detection with multiple attempts
    response = None
    for attempt in range(3):  # 3 attempts over 6 additional seconds
        dest_messages = await client.get_messages(DESTINATION_BOT, limit=15)  # Check more messages
        if dest_messages:
            # Look for a message that contains our token or scanner data
            for msg in dest_messages:
                if msg.text and (
                    token_address in msg.text or
                    (len(token_address) > 10 and token_address[:10] in msg.text) or
                    any(indicator in msg.text for indicator in ["**$", "MC:", "Age:", "Scans:", "💰", "🕒"])
                ):
                    response = msg
                    total_time = 5 + (attempt + 1) * 2
                    logger.info(f"🔍 HYBRID: Found complete response in {total_time}s for token {token_address}")
                    break

            if response:
                break
            else:
                if attempt < 2:  # Don't wait after the last attempt
                    logger.info(f"⏳ No response yet, waiting 2 more seconds... (attempt {attempt + 1}/3)")
                    await asyncio.sleep(2)

    if response:
        logger.info(f"📩 ULTRA-FAST: Processing response...")
        combined_data = extract_data_from_scanner_response(response.text, token_address, source_age)
        await export_to_excel(combined_data)
        logger.info("✅ ULTRA-FAST: Excel updated")
    else:
        logger.warning("⚠️ ULTRA-FAST: No response, saving minimal data")
        # Still save the token with minimal data
        minimal_data = {
            "Token Address": token_address,
            "Token Name": "N/A",
            "Warnings": "N/A",
            "Age": "N/A",
            "Source Age": source_age,
            "MC": "N/A",
            "T-MC": "N/A",
            "Liq": "N/A",
            "Liq SOL": "N/A",
            "Volume": "N/A",
            "Price": "N/A",
            "Dex Paid": "N/A",
            "Scans": "N/A",
            "Hodls": "N/A",
            "High": "N/A",
            "Snipers": "N/A",
            "Snipers Percentage": "N/A",
            "Dev": "N/A",
            "Top Holders": "N/A",
            "LP Burnt": "N/A",
            "Bundled": "N/A",
            "Airdrop": "N/A",
            "Burnt": "N/A",
            "Sold": "N/A",
            "Made": "N/A",
            "Bond": "N/A",
            "First 20 Percentage": "N/A",
            "First 20": "N/A",
            "Whale Fish Pattern": "N/A"
        }
        await export_to_excel(minimal_data)
        logger.info("✅ ULTRA-FAST: Minimal data saved")

    logger.info("🚀 ULTRA-FAST: Processing complete")

async def main():
    """Main function"""
    global client

    logger.info("🚀 Starting HYBRID ULTRA-FAST ATH Scanner")
    logger.info(f"📍 Source: {SOURCE_USERNAME}")
    logger.info(f"📍 Destination: {DESTINATION_BOT}")
    logger.info(f"📊 Excel File: {EXCEL_FILE}")
    logger.info("⚡ HYBRID MODE: INSTANT message detection + 5s bot timeout for complete data")
    logger.info("=" * 75)

    # Initialize Excel file
    initialize_excel()

    # Initialize Telegram client
    client = TelegramClient(SESSION_FILE.replace('.session', ''), API_ID, API_HASH)

    try:
        # Start the client
        await client.start(phone=PHONE)
        logger.info("✅ Client started successfully")

        # Resolve the source channel entity
        try:
            source_entity = await client.get_entity(SOURCE_USERNAME)
            logger.info(f"✅ Resolved source channel: {source_entity.title} (ID: {source_entity.id})")
        except Exception as e:
            logger.error(f"❌ Failed to resolve source channel {SOURCE_USERNAME}: {e}")
            return

        # Register the HYBRID ULTRA-FAST event handler for new messages
        client.add_event_handler(message_handler, events.NewMessage(chats=[source_entity]))
        logger.info("⚡ HYBRID LISTENING: INSTANT detection + 5s bot timeout for complete data!")

        # Keep the client running to listen for events
        await client.run_until_disconnected()

    except Exception as e:
        logger.error(f"❌ Error in main: {e}")
    finally:
        if client:
            await client.disconnect()
            logger.info("🔌 Client disconnected")

if __name__ == "__main__":
    asyncio.run(main())
