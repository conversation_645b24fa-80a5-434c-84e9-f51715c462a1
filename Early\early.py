from telethon import TelegramClient, events
from telethon.tl.types import MessageEntityTextUrl, MessageEntityUrl, InputMessagesFilterEmpty
import re
import asyncio
import os
import time
from openpyxl import Workbook, load_workbook
from openpyxl.styles import <PERSON><PERSON><PERSON>ill
from datetime import datetime
import logging
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Step 1: Configuration settings from environment variables
API_ID = os.getenv("API_ID")
API_HASH = os.getenv("API_HASH")
PHONE = os.getenv("PHONE")

# Directly use credentials
api_id = API_ID
api_hash = API_HASH
phone_number = PHONE

# Step 2: Source and Destination IDs
source_username = "@solearlytrending"  # Username of the source channel
destination_username = "@soul_scanner_bot"  # Username of the destination bot

# Step 3: Regex patterns for token extraction
token_pattern_type1 = r"https://t\.me/soul_sniper_bot\?start=15_([A-Za-z0-9]{43,44})"  # Type 1 token pattern
token_pattern_type2 = r"https://www\.geckoterminal\.com/solana/pools/([A-Za-z0-9]{43,44})"  # Updated Type 2 token pattern
token_pattern_pump = r"([A-Za-z0-9]{39,40}pump)"  # Pattern for tokens ending with "pump"

# Step 4: Message filtering patterns
type1_pattern = ["New Trending"]  # Process Type 1 messages containing these words
type2_pattern = ["is up"]  # Process Type 2 messages containing these words

# Step 5: Rate Limiting (in seconds)
RATE_LIMIT_DELAY = 2  # Reduced to 2 seconds for faster processing
MAX_RETRIES = 8  # Increased for better reliability
CATCH_UP_MESSAGES = 100  # Increased number of past messages to fetch on startup
KEEPALIVE_INTERVAL = 30  # Check connection every 30 seconds instead of 60
CONNECTION_CHECK_INTERVAL = 5  # Check if we're receiving messages every 5 minutes
MISSED_MESSAGE_THRESHOLD = 300  # If no messages received for 5 minutes, refetch recent messages

# Step 6: Excel file setup with combined columns (including Profit column)
EXCEL_FILE = "earlytrading_data.xlsx"
HEADERS = [
    "Timestamp", "Token Address", "Token Name", "Profit", "Made", "Fish", "X URL", "Warnings", "Age", "MC", "T-MC", "Liq", "Liq SOL",
    "Scans", "Hodls", "High", "Snipers", "Snipers Percentage", "Dev", "Sniped",
    "Top Holders", "LP", "First", "First Percentage", "Sold", "First Source",
    "Detected Words", "Source Age",
]

# Step 7: Conditional formatting colors
GREEN_FILL = PatternFill(start_color="00FF00", end_color="00FF00", fill_type="solid")  # 10X
BLUE_FILL = PatternFill(start_color="0000FF", end_color="0000FF", fill_type="solid")   # >20X
ORANGE_FILL = PatternFill(start_color="FFA500", end_color="FFA500", fill_type="solid") # 7X-7.9X
RED_FILL = PatternFill(start_color="FF0000", end_color="FF0000", fill_type="solid")    # 5X-5.9X
PURPLE_FILL = PatternFill(start_color="800080", end_color="800080", fill_type="solid") # 3X-3.9X
YELLOW_FILL = PatternFill(start_color="FFFF00", end_color="FFFF00", fill_type="solid") # 2X-2.9X
GREY_FILL = PatternFill(start_color="808080", end_color="808080", fill_type="solid")   # 50%-99%

# Step 8: Target group configurations
# Removed keyword whitelist filter - accept all keywords
TARGET_WHITELIST = []  # Empty list to disable filtering

TARGET_ENTITIES = [
    'gmgnsignals', 'gmgnsignalsol'
]

# Initialize important global variables
processed_tokens = set()
processing_lock = asyncio.Lock()
processing_queue = asyncio.Queue()
is_processing = False
client = None  # Will be initialized in main()
last_message_time = time.time()  # Track when we last received a message

# Initialize Excel file if it doesn't exist
def initialize_excel():
    """Create Excel file with headers if it doesn't exist"""
    if not os.path.exists(EXCEL_FILE):
        logger.info(f"Creating new Excel file: {EXCEL_FILE}")
        wb = Workbook()
        ws = wb.active
        ws.append(HEADERS)
        wb.save(EXCEL_FILE)
    else:
        # Check if Profit column exists, if not add it
        wb = load_workbook(EXCEL_FILE)
        ws = wb.active
        headers = [cell.value for cell in ws[1]]
        if "Profit" not in headers:
            logger.info("Adding Profit column to existing Excel file")
            profit_col_idx = len(headers) + 1
            ws.cell(row=1, column=profit_col_idx, value="Profit")
            wb.save(EXCEL_FILE)

def clear_terminal():
    """Clear the terminal screen."""
    os.system("cls" if os.name == "nt" else "clear")

def should_forward_token(data):
    """Custom filter function with all specified criteria"""
    try:
        # All filters removed - always return True
        return True
    except Exception as e:
        logger.error(f"❌ Error in should_forward_token: {e}")
        return True

def detect_keywords(text):
    """Check if message contains any target whitelist words."""
    if not text:
        return []
    text_lower = text.lower()
    return [word for word in TARGET_WHITELIST if word.lower() in text_lower]

async def search_token_in_groups(token_address):
    """Search for the token in target groups and collect data."""
    earliest_source = None
    earliest_date = None
    detected_words = []
    source_age = None

    for entity in TARGET_ENTITIES:
        try:
            await asyncio.sleep(3)  # Rate limiting between groups
            logger.info(f"  Searching for {token_address} in {entity}...")

            messages = await client.get_messages(
                entity,
                search=token_address,
                limit=50,
                filter=InputMessagesFilterEmpty()
            )

            # Check messages in chronological order (oldest first)
            for msg in reversed(messages):
                if token_address in msg.text:
                    if (earliest_date is None) or (msg.date < earliest_date):
                        earliest_date = msg.date
                        earliest_source = entity
                        detected_words = detect_keywords(msg.text)
                        age_match = re.search(r'Open:\s*\*\*([^*]+)\*\*\s*\*\*ago', msg.text, re.IGNORECASE)
                        source_age = age_match.group(1).strip() if age_match else None
        except Exception as e:
            logger.error(f"    Error searching {entity}: {e}")
            await asyncio.sleep(3)  # Delay after error

    return {
        "First Source": earliest_source if earliest_source else "Not Found",
        "Detected Words": ", ".join(detected_words) if detected_words else "None",
        "Source Age": source_age if source_age else "Not Found"
    }

def extract_data_from_scanner_response(message_text, token_address):
    """Extract data from the scanner bot's message."""
    data = {
        "Token Address": token_address,
        "Token Name": "N/A",
        "Profit": "Lost",
        "Made": "N/A",
        "Fish": "N/A",
        "X URL": "N/A",        
        "Warnings": "N/A",
        "Age": "N/A",
        "MC": "N/A",
        "T-MC": "N/A",
        "Liq": "N/A",
        "Liq SOL": "N/A",
        "Scans": "N/A",
        "Hodls": "N/A",
        "High": "N/A",
        "Snipers": "N/A",
        "Snipers Percentage": "N/A",
        "Dev": "N/A",
        "Sniped": "N/A",
        "Top Holders": "N/A",
        "LP": "N/A",
        "First": "N/A",
        "First Percentage": "N/A",
        "Sold": "N/A",
        "First Source": "N/A",
        "Detected Words": "N/A",
        "Source Age": "N/A"
    }

    # Token Name
    token_name_match = re.search(r"\*\*\$([A-Za-z0-9]+)\*\*", message_text)
    if token_name_match:
        data["Token Name"] = token_name_match.group(1)

    # Warnings
    warnings = re.findall(r"(🚨|⚠️)?\s*__([^__]+)__", message_text)
    if warnings:
        filtered_warnings = [
            f"{icon} {warning}" if icon else warning
            for icon, warning in warnings
            if any(keyword.lower() in warning.lower() 
                 for keyword in ["High", "Migrating", "Low", "Dev", "Sniped", "Abnormal"])
        ]
        if filtered_warnings:
            data["Warnings"] = " | ".join(filtered_warnings)

    # Age
    age_match = re.search(r"🕒 \*\*Age:\*\* (\d+[smhdw]+\s*\d*[smhdw]*)", message_text)
    if age_match:
        data["Age"] = age_match.group(1)

    # Market Cap
    mc_match = re.search(r"💰 \*\*MC:\*\* (\$\d[\d,.]*[KMB]?)", message_text)
    if mc_match:
        data["MC"] = mc_match.group(1)

    # T-MC
    t_mc_match = re.search(r"🔝 __(\$\d[\d,.]*[KMB]?)__", message_text)
    if t_mc_match:
        data["T-MC"] = t_mc_match.group(1)

    # Liquidity
    liq_match = re.search(r"💧 \*\*(v?Liq):\*\* (\$\d[\d,.]*[KMB]?) \((\d+\.?\d* SOL)\)", message_text)
    if liq_match:
        liq_type = liq_match.group(1)
        liq_value = liq_match.group(2)
        liq_sol = liq_match.group(3)
        if liq_type == "vLiq":
            liq_value += "-v"
        data["Liq"] = liq_value
        data["Liq SOL"] = liq_sol

    # Scans
    scans_match = re.search(r"⚡ \*\*Scans:\*\* (\d+)", message_text)
    if scans_match:
        data["Scans"] = scans_match.group(1)

    # Hodls
    hodls_match = re.search(r"\[\*\*Hodls\*\*\]\([^)]+\): (\d+)", message_text)
    if hodls_match:
        data["Hodls"] = hodls_match.group(1)

    # High
    high_match = re.search(r"┗ High:\s*\[(\d+\.?\d*%)]", message_text)
    if high_match:
        data["High"] = high_match.group(1)

    # Snipers
    snipers_match = re.search(r"🔫 \*\*Snipers:\*\* (\d+) • (\d+\.?\d*%)", message_text)
    if snipers_match:
        data["Snipers"] = snipers_match.group(1)
        data["Snipers Percentage"] = snipers_match.group(2)

    # Dev
    dev_match = re.search(r"🛠️ Dev.*?(\d+ SOL)", message_text)
    if dev_match:
        data["Dev"] = dev_match.group(1)

    # Sniped
    sniped_match = re.search(r"┣ Sniped: (\d+\.?\d*%)", message_text)
    if sniped_match:
        data["Sniped"] = sniped_match.group(1)

    # Top Holders
    top_holders_match = re.search(r"\[\*\*Hodls\*\*\]\([^)]+\): \d+ • Top: (\d+\.?\d*%)", message_text)
    if top_holders_match:
        data["Top Holders"] = top_holders_match.group(1)

    # LP
    lp_match = re.search(r"\*\*:\*\* (\d+%) Burnt", message_text)
    if lp_match:
        data["LP"] = lp_match.group(1)

    # First
    first_match = re.search(r"\*\*:\*\* (\d+)\s*Fresh", message_text)
    if first_match:
        data["First"] = first_match.group(1)

    # First Percentage
    first_percentage_match = re.search(r"Fresh\s*•\s*(\d+%)", message_text)
    if first_percentage_match:
        data["First Percentage"] = first_percentage_match.group(1)

    # Sold
    sold_match = re.search(r"\| Sold:\s*(\d+\.?\d*%)", message_text)
    if sold_match:
        data["Sold"] = sold_match.group(1)

    # Burnt
    burnt_match = re.search(r"\| Burnt:\s*(\d+\.?\d*%)", message_text)
    if burnt_match:
        data["Burnt"] = burnt_match.group(1)


    # Made (updated pattern)
    made_match = re.search(r"Made:\s*(\d+)", message_text)
    if made_match:
        data["Made"] = made_match.group(1)


    # First Fish/Symbol
    fish_match = re.search(r"First[^\|]+\|\s*([\d,]+ [^\d\s•]+)", message_text)
    if fish_match:
        data["Fish"] = fish_match.group(1)    
    return data

async def export_to_excel(data):
    """Export combined data to Excel with thread-safety."""
    async with processing_lock:
        try:
            wb = load_workbook(EXCEL_FILE)
            ws = wb.active

            # Check if token already exists in Excel
            token_exists = False
            token_row = None

            for row_idx, row in enumerate(ws.iter_rows(min_row=2, values_only=True), start=2):
                if row[1] == data["Token Address"]:  # Token Address is in column 2
                    token_exists = True
                    token_row = row_idx
                    break

            if token_exists:
                # Check if this is just a profit update (Type 2 message)
                if len(data.keys()) <= 3 and "Profit" in data and data.get("Profit") != "N/A":
                    # This is just a profit update, just update the profit column
                    profit_col_idx = HEADERS.index("Profit") + 1
                    ws.cell(row=token_row, column=profit_col_idx, value=data["Profit"])
                    cell = ws.cell(row=token_row, column=profit_col_idx)

                    if '%' in data["Profit"]:
                        profit_value = float(data["Profit"].rstrip('%'))
                        if 50 <= profit_value < 100:
                            cell.fill = GREY_FILL
                    elif 'X' in data["Profit"]:
                        profit_value = float(data["Profit"].rstrip('X'))
                        if 2 <= profit_value < 3:
                            cell.fill = YELLOW_FILL
                        elif 3 <= profit_value < 4:
                            cell.fill = PURPLE_FILL
                        elif 5 <= profit_value < 6:
                            cell.fill = RED_FILL
                        elif 7 <= profit_value < 8:
                            cell.fill = ORANGE_FILL
                        elif 10 <= profit_value < 20:
                            cell.fill = GREEN_FILL
                        elif profit_value >= 20:
                            cell.fill = BLUE_FILL

                    # Update X URL if provided
                    if "X URL" in data and data["X URL"] != "N/A":
                        x_url_col_idx = HEADERS.index("X URL") + 1
                        ws.cell(row=token_row, column=x_url_col_idx, value=data["X URL"])

                    logger.info(f"✅ Updated profit data for existing token {data['Token Address']}")
                elif "Token Name" in data and data["Token Name"] != "N/A":
                    # This is a full token data update from scanner bot, skip if token already exists
                    logger.info(f"⏩ Token {data['Token Address']} already exists, skipping scanner data update")
                else:
                    # This is a partial update with fields other than profit, apply them
                    for col_idx, field in enumerate(HEADERS[1:], start=2):
                        if field in data and data[field] != "N/A":
                            ws.cell(row=token_row, column=col_idx, value=data[field])

                    logger.info(f"✅ Updated specific fields for existing token {data['Token Address']}")
            else:
                row = [
                    datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    data["Token Address"],
                    data["Token Name"],
                    data["Profit"],
                    data["Made"],
                    data["Fish"],
                    data["X URL"],
                    data["Warnings"],
                    data["Age"],
                    data["MC"],
                    data["T-MC"],
                    data["Liq"],
                    data["Liq SOL"],
                    data["Scans"],
                    data["Hodls"],
                    data["High"],
                    data["Snipers"],
                    data["Snipers Percentage"],
                    data["Dev"],
                    data["Sniped"],
                    data["Top Holders"],
                    data["LP"],
                    data["First"],
                    data["First Percentage"],
                    data["Sold"],
                    data["First Source"],
                    data["Detected Words"],
                    data["Source Age"]
                ]
                ws.append(row)
                logger.info(f"✅ Added new row for token {data['Token Address']}")

            wb.save(EXCEL_FILE)
            logger.info(f"✅ Data saved to {EXCEL_FILE}")
        except Exception as e:
            logger.error(f"❌ Failed to export data to Excel: {e}")
            raise

def extract_urls_from_entities(message):
    """Extract URLs from message entities."""
    urls = []
    if message.entities:
        for entity in message.entities:
            if isinstance(entity, (MessageEntityTextUrl, MessageEntityUrl)):
                url = entity.url if isinstance(entity, MessageEntityTextUrl) else message.text[entity.offset:entity.offset + entity.length]
                urls.append(url)
    return urls

def extract_token_from_url(url, pattern):
    """Extract token from URL using the specified regex pattern."""
    token_matches = re.findall(pattern, url)
    return token_matches[0] if token_matches else None

def extract_profit_from_message(message_text):
    """Extract profit value from Type 2 message with enhanced pattern matching."""
    if not message_text:
        return None
        
    # 1. Match standard format: "is up 53%" or "is up 5.3X" with flexible spacing/formatting
    profit_match = re.search(r"is\s+up\s*[^\d]*(\d+\.?\d*)([%Xx])", message_text, re.IGNORECASE)
    if profit_match:
        value = profit_match.group(1)
        unit = profit_match.group(2).upper()  # Normalize unit to uppercase
        return f"{value}{unit}"
    
    # 2. Try arrow notation format: "$50.6K —> $77.1K 💵" with flexible arrow styles
    mc_match = re.search(r"\$(\d+\.?\d*)([KMB])?\s*(?:—>|->|=>|⇒)\s*\$(\d+\.?\d*)([KMB])?", message_text)
    if mc_match:
        # Extract values and convert to float
        start_value = float(mc_match.group(1))
        start_unit = mc_match.group(2) or ''
        end_value = float(mc_match.group(3))
        end_unit = mc_match.group(4) or ''
        
        # Convert to same units
        multipliers = {'': 1, 'K': 1000, 'M': 1000000, 'B': 1000000000}
        start_amount = start_value * multipliers[start_unit]
        end_amount = end_value * multipliers[end_unit]
        
        # Calculate percentage or X increase
        if end_amount > start_amount:
            increase_ratio = end_amount / start_amount
            if increase_ratio >= 2:
                # X format for 2X or more
                return f"{increase_ratio:.1f}X"
            else:
                # Percentage format for less than 2X
                percentage = (increase_ratio - 1) * 100
                return f"{percentage:.0f}%"
    
    # 3. Look for a specific pattern with the words "up" and percentage/X value near each other
    up_val_match = re.search(r"up\s+(?:by\s+)?(\d+\.?\d*)([%Xx])", message_text, re.IGNORECASE)
    if up_val_match:
        value = up_val_match.group(1)
        unit = up_val_match.group(2).upper()
        return f"{value}{unit}"
    
    # 4. Look for standalone values that might represent profits
    # This is the most aggressive matching and only use if nothing else works
    profit_indicators = ["profit", "gain", "up", "increased", "jumped", "gained"]
    if any(indicator in message_text.lower() for indicator in profit_indicators):
        # Look for percentage or X values
        standalone_match = re.search(r"(\d+\.?\d*)([%Xx])", message_text)
        if standalone_match:
            value = standalone_match.group(1)
            unit = standalone_match.group(2).upper()
            return f"{value}{unit}"
    
    return None

async def process_type1_message(message):
    """Process Type 1 message (New Trending)."""
    try:
        # First, extract all URLs from the message
        urls = []

        # Extract from plain text
        urls.extend(re.findall(token_pattern_type1, message.text))

        # Extract tokens ending with "pump" directly from message text
        pump_matches = re.findall(token_pattern_pump, message.text)
        if pump_matches:
            logger.info(f"🔍 Found token addresses ending with 'pump': {pump_matches}")
            urls.extend(pump_matches)

        # Extract from message entities
        entity_urls = extract_urls_from_entities(message)
        for url in entity_urls:
            token = extract_token_from_url(url, token_pattern_type1)
            if token:
                urls.append(token)

        if not urls:
            logger.info("❌ No valid token URLs found in the message")
            return

        # Process each token
        for token_address in urls:
            if token_address in processed_tokens:
                logger.info(f"⏩ Token {token_address} already processed, skipping")
                continue

            processed_tokens.add(token_address)
            logger.info(f"🔄 Processing token: {token_address}")

            # Forward token to soul_scanner_bot
            for attempt in range(MAX_RETRIES):
                try:
                    logger.info(f"📤 Sending token to {destination_username} (attempt {attempt+1}/{MAX_RETRIES})")
                    await client.send_message(destination_username, token_address)

                    # Wait for response
                    response = None
                    for _ in range(10):  # Try for up to 10 seconds
                        await asyncio.sleep(1)
                        async for msg in client.iter_messages(destination_username, limit=5):
                            if token_address in msg.text:
                                response = msg
                                break
                        if response:
                            break

                    if not response:
                        logger.warning(f"⏳ No response from bot after attempt {attempt+1}, retrying...")
                        if attempt < MAX_RETRIES - 1:
                            await asyncio.sleep(RATE_LIMIT_DELAY)
                            continue
                        else:
                            logger.error(f"❌ Failed to get response after {MAX_RETRIES} attempts")
                            break

                    # Extract data from response
                    scanner_data = extract_data_from_scanner_response(response.text, token_address)
                    
                    # Extract X URL from message entities
                    x_url = "N/A"
                    if hasattr(message, 'entities') and message.entities:
                        for entity in message.entities:
                            if isinstance(entity, MessageEntityTextUrl):
                                if 'x.com' in entity.url or 'twitter.com' in entity.url:
                                    x_url = entity.url
                                    logger.info(f"🐦 Found X URL: {x_url}")
                                    break
                    scanner_data["X URL"] = x_url
                    
                    # Filter tokens based on criteria
                    if not should_forward_token(scanner_data):
                        logger.info(f"⏩ Token filtered: {token_address}")
                        continue

                    # Search for token in target groups
                    group_data = await search_token_in_groups(token_address)
                    scanner_data.update(group_data)

                    # Save data to Excel
                    await export_to_excel(scanner_data)

                    # Rate limiting
                    await asyncio.sleep(RATE_LIMIT_DELAY)
                    break  # Successfully processed this token, break retry loop

                except Exception as e:
                    logger.error(f"❌ Error processing token {token_address}: {e}")
                    if attempt < MAX_RETRIES - 1:
                        await asyncio.sleep(RATE_LIMIT_DELAY)
                    else:
                        logger.error(f"❌ Failed to process token after {MAX_RETRIES} attempts")

    except Exception as e:
        logger.error(f"❌ Error in process_type1_message: {e}")

async def process_type2_message(message):
    """Process Type 2 message (is up)."""
    try:
        # Extract token from URL
        urls = []
        message_text = message.text or ""

        # Extract from plain text - look for GeckoTerminal URLs with flexible pattern
        # This handles both www.geckoterminal.com and geckoterminal.com formats
        for url in re.findall(r'https?://(?:www\.)?geckoterminal\.com/solana/pools/[a-zA-Z0-9]+', message_text):
            # Extract the token ID from the GeckoTerminal URL
            token_match = re.search(r'pools/([a-zA-Z0-9]+)', url)
            if token_match:
                token = token_match.group(1)
                urls.append((url, token))
        
        # Look for any token addresses directly in the message
        sol_addr_matches = re.findall(r'\b([A-Za-z0-9]{43,44})\b', message_text)
        for token in sol_addr_matches:
            if (f"direct_mention:{token}", token) not in urls:
                urls.append((f"direct_mention:{token}", token))

        # Look for tokens ending with "pump" directly in the message
        pump_matches = re.findall(token_pattern_pump, message_text)
        for token in pump_matches:
            if (f"pump_token:{token}", token) not in urls:
                urls.append((f"pump_token:{token}", token))
                logger.info(f"🔍 Found token ending with 'pump': {token}")

        # Extract from message entities with more flexible patterns
        entity_urls = extract_urls_from_entities(message)
        for url in entity_urls:
            # Try GeckoTerminal format with case-insensitive match
            token_match = re.search(r'geckoterminal\.com/solana/pools/([a-zA-Z0-9]+)', url, re.IGNORECASE)
            if token_match:
                token = token_match.group(1)
                if (url, token) not in urls:
                    urls.append((url, token))
            else:
                # Try traditional token_pattern_type2 format
                token = extract_token_from_url(url, token_pattern_type2)
                if token and (url, token) not in urls:
                    urls.append((url, token))

        if not urls:
            logger.info("❌ No valid token URLs or addresses found in the message")
            return

        # Extract profit percentage with enhanced pattern matching
        profit_value = extract_profit_from_message(message_text)
        
        # Try additional profit patterns if the main one fails
        if not profit_value:
            # Look for patterns like "$50.6K —> $77.1K" and calculate percentage
            mc_match = re.search(r"\$(\d+\.?\d*)([KMB])?\s*—>\s*\$(\d+\.?\d*)([KMB])?", message_text)
            if mc_match:
                # Extract values and convert to float
                start_value = float(mc_match.group(1))
                start_unit = mc_match.group(2) or ''
                end_value = float(mc_match.group(3))
                end_unit = mc_match.group(4) or ''
                
                # Convert to same units
                multipliers = {'': 1, 'K': 1000, 'M': 1000000, 'B': 1000000000}
                start_amount = start_value * multipliers[start_unit]
                end_amount = end_value * multipliers[end_unit]
                
                # Calculate percentage or X increase
                if end_amount > start_amount:
                    increase_ratio = end_amount / start_amount
                    if increase_ratio >= 2:
                        # X format for 2X or more
                        profit_value = f"{increase_ratio:.1f}X"
                    else:
                        # Percentage format for less than 2X
                        percentage = (increase_ratio - 1) * 100
                        profit_value = f"{percentage:.0f}%"

        # Still no profit value found
        if not profit_value:
            # Try a more generic approach - look for any numbers followed by % or X
            profit_matches = re.findall(r'(\d+\.?\d*)([%Xx])', message_text)
            if profit_matches:
                max_value = 0
                max_unit = '%'
                
                for value_str, unit in profit_matches:
                    unit = unit.upper()
                    value = float(value_str)
                    if unit == 'X' and value > max_value:
                        max_value = value
                        max_unit = 'X'
                    elif unit == '%' and value > max_value and max_unit != 'X':
                        max_value = value
                        max_unit = '%'
                
                profit_value = f"{max_value}{max_unit}"

        if not profit_value:
            logger.info("❌ Could not extract profit value from the message")
            profit_value = "N/A"  # Use N/A instead of failing
        else:
            logger.info(f"📊 Extracted profit: {profit_value} for token(s): {[token for _, token in urls]}")

        # Extract X URL from message entities
        x_url = "N/A"
        if hasattr(message, 'entities') and message.entities:
            for entity in message.entities:
                if isinstance(entity, MessageEntityTextUrl):
                    if 'x.com' in entity.url or 'twitter.com' in entity.url:
                        x_url = entity.url
                        logger.info(f"🐦 Found X URL: {x_url}")
                        break
        
        # Update Excel with profit data
        for _, token_address in urls:
            data = {
                "Token Address": token_address,
                "Profit": profit_value,
                "X URL": x_url
            }

            try:
                await export_to_excel(data)
                logger.info(f"✅ Updated profit for token: {token_address}")
            except Exception as e:
                logger.error(f"❌ Failed to update profit for token {token_address}: {e}")

    except Exception as e:
        logger.error(f"❌ Error in process_type2_message: {e}")
        # Add full message logging for debugging
        if 'message_text' in locals():
            logger.error(f"Message content: {message_text}")

async def message_handler(event):
    """Handle all incoming messages from the source channel."""
    global last_message_time
    
    # Update last message time
    last_message_time = time.time()
    
    # Log the message arrival immediately
    message_text = event.message.text if hasattr(event, 'message') and hasattr(event.message, 'text') else "No text"
    logger.info(f"📨 Received message: {message_text[:30]}...")
    
    # Add to processing queue with higher priority for new messages
    await processing_queue.put((0, event))  # Lower number = higher priority
    logger.info(f"📥 Queued new message. Current queue size: {processing_queue.qsize()}")

async def process_queue_worker():
    """Process messages from the queue one at a time."""
    global is_processing

    while True:
        try:
            if not is_processing and not processing_queue.empty():
                is_processing = True
                priority, event = await processing_queue.get()

                try:
                    # Handle both proper Message objects and string objects
                    if hasattr(event, 'message'):
                        # This is a proper event object
                        message = event.message
                        message_text = message.text if hasattr(message, 'text') else ""
                    elif isinstance(event, str):
                        # This is just a string, create a simple object to mimic the message structure
                        logger.info(f"Processing string message instead of Message object")
                        message_text = event
                        
                        # Create a simple object that mimics the necessary attributes
                        class SimpleMessage:
                            def __init__(self, text):
                                self.text = text
                                self.entities = []
                                
                        message = SimpleMessage(message_text)
                    else:
                        # Neither a proper event nor a string, log and skip
                        logger.error(f"Unknown message type: {type(event)}")
                        continue
                    
                    logger.info(f"📩 Processing message: {message_text[:50]}...")
                    
                    # Extract all URLs and pattern matches from the message
                    urls = extract_urls_from_entities(message) 
                    # Add text URLs that might not be in entities
                    text_urls = re.findall(r'https?://\S+', message_text)
                    for url in text_urls:
                        if url not in urls:
                            urls.append(url)
                            
                    # Enhanced pattern detection - Check patterns in the entire message
                    has_type1_pattern = any(pattern in message_text for pattern in type1_pattern)
                    has_type2_pattern = any(pattern in message_text.lower() for pattern in type2_pattern)
                    
                    # Extract tokens from URLs
                    type1_tokens = []
                    type2_tokens = []
                    
                    for url in urls:
                        # Try Type 1 pattern (soul_sniper_bot urls)
                        token = extract_token_from_url(url, token_pattern_type1)
                        if token and token not in type1_tokens:
                            type1_tokens.append(token)
                            
                        # Try Type 2 pattern (GeckoTerminal URLs)
                        token_match = re.search(r'geckoterminal\.com/solana/pools/([a-zA-Z0-9]+)', url)
                        if token_match:
                            token = token_match.group(1)
                            if token and token not in type2_tokens:
                                type2_tokens.append(token)

                    # Process Type 1 messages (New Trending)
                    if has_type1_pattern or type1_tokens:
                        logger.info("🔍 Type 1 message detected (New Trending)")
                        await process_type1_message(message)
                    
                    # Process Type 2 messages (is up)
                    elif has_type2_pattern or type2_tokens:
                        logger.info("🔍 Type 2 message detected (is up)")
                        await process_type2_message(message)
                    
                    else:
                        # More aggressive parsing for missed messages
                        # Look for any GeckoTerminal URL
                        gecko_tokens = []
                        for url in urls:
                            if "geckoterminal.com" in url:
                                token_match = re.search(r'pools/([a-zA-Z0-9]+)', url)
                                if token_match:
                                    gecko_tokens.append(token_match.group(1))
                        
                        if gecko_tokens:
                            logger.info("🔍 Possible profit update detected (GeckoTerminal URL found)")
                            await process_type2_message(message)
                        else:
                            logger.info("⏩ Message does not match any patterns, skipping")

                except Exception as e:
                    logger.error(f"❌ Error processing message: {e}")
                    # Log the full message for debugging
                    logger.error(f"Message content: {message_text}")

                finally:
                    is_processing = False
                    processing_queue.task_done()

            else:
                await asyncio.sleep(0.5)  # Faster check interval (0.5s instead of 1s)

        except Exception as e:
            logger.error(f"❌ Error in queue worker: {e}")
            is_processing = False
            await asyncio.sleep(1)

async def fetch_recent_messages():
    """Fetch recent messages from the source channel to process any that were missed."""
    try:
        logger.info(f"📡 Fetching last {CATCH_UP_MESSAGES} messages from {source_username} to catch up...")
        recent_messages = await client.get_messages(source_username, limit=CATCH_UP_MESSAGES)
        
        if not recent_messages:
            logger.warning("⚠️ No recent messages found to process")
            return
            
        # Process messages in chronological order (oldest first)
        queue_count = 0
        for message in reversed(recent_messages):
            # Skip messages that don't have text
            if not hasattr(message, 'text') or not message.text:
                continue
                
            # Skip messages that are too old (more than 1 hour)
            if hasattr(message, 'date'):
                message_age = datetime.now() - message.date.replace(tzinfo=None)
                if message_age.total_seconds() > 3600:  # Skip messages older than 1 hour
                    continue
            
            # Create a simple event-like object to match what we get from event handlers
            class SimpleEvent:
                def __init__(self, msg):
                    self.message = msg
                    
            event = SimpleEvent(message)
            
            # Add to processing queue with lower priority (catch-up messages)
            await processing_queue.put((1, event))  # 1 = lower priority than new messages
            queue_count += 1
            
        logger.info(f"✅ Queued {queue_count} historical messages for processing")
    except Exception as e:
        logger.error(f"❌ Error fetching recent messages: {e}")

async def check_for_missed_messages():
    """Periodic check for missed messages if we haven't received any recently."""
    global last_message_time
    
    while True:
        try:
            # Wait for the check interval
            await asyncio.sleep(CONNECTION_CHECK_INTERVAL * 60)  # Convert minutes to seconds
            
            # If no messages received recently, fetch recent messages again
            current_time = time.time()
            if current_time - last_message_time > MISSED_MESSAGE_THRESHOLD:
                logger.warning(f"⚠️ No messages received in the last {MISSED_MESSAGE_THRESHOLD/60:.1f} minutes! Fetching recent messages...")
                await fetch_recent_messages()
                last_message_time = current_time  # Reset timer after fetching
        except Exception as e:
            logger.error(f"❌ Error in missed message checker: {e}")
            await asyncio.sleep(60)  # Wait a bit after error and try again
            
async def main():
    """Main entry point for the application."""
    global client, last_message_time
    
    # Initialize the last_message_time to the current time
    last_message_time = time.time()

    # Print startup information
    print("\n" + "="*50)
    print("Solana Token Tracker Bot")
    print("="*50)
    print(f"Source Channel: {source_username}")
    print(f"Destination Bot: {destination_username}")
    print(f"Excel File: {EXCEL_FILE}")
    print("="*50 + "\n")

    # Initialize Excel file
    initialize_excel()

    # Create and start the Telegram client
    logger.info("🚀 Starting Telegram client...")
    client = TelegramClient("earlytrading_data", api_id, api_hash)

    try:
        await client.start(phone_number)
        logger.info("✅ Client started successfully")

        # First fetch recent messages to catch up on any we might have missed
        await fetch_recent_messages()

        # Set up event handler for source channel
        client.add_event_handler(
            message_handler,
            events.NewMessage(chats=[source_username])
        )

        # Set up edited message handler (also important to catch updates)
        client.add_event_handler(
            message_handler,
            events.MessageEdited(chats=[source_username])
        )

        # Start the queue worker
        logger.info("🔄 Starting queue worker...")
        asyncio.create_task(process_queue_worker())
        
        # Start the missed message checker
        logger.info("🔄 Starting missed message checker...")
        asyncio.create_task(check_for_missed_messages())

        logger.info(f"🎯 Bot is now monitoring {source_username} for new and edited messages")

        # Keep the bot running with periodic reconnection checks
        while True:
            if not client.is_connected():
                logger.warning("⚠️ Client disconnected. Attempting to reconnect...")
                await client.connect()
                
                # Fetch messages after reconnection
                logger.info("🔄 Reconnected, fetching recent messages...")
                await fetch_recent_messages()
                
            await asyncio.sleep(KEEPALIVE_INTERVAL)  # Check connection more frequently

    except Exception as e:
        logger.error(f"❌ Error in main: {e}")
    finally:
        if client.is_connected():
            await client.disconnect()
        logger.info("👋 Bot has been stopped")

if __name__ == "__main__":
    # Run the bot
    asyncio.run(main())